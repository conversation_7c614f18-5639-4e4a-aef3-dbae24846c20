<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE html>
<html b:css='false' b:defaultwidgetversion='2' b:layoutsVersion='3' b:responsive='true' expr:dir='data:blog.languageDirection' expr:lang='data:blog.locale' xmlns='http://www.w3.org/1999/xhtml' xmlns:b='http://www.google.com/2005/gml/b' xmlns:data='http://www.google.com/2005/gml/data' xmlns:expr='http://www.google.com/2005/gml/expr'>
  <head><b:attr expr:value='data:view.isSingleItem ? "og: http://ogp.me/ns# fb: http://ogp.me/ns/fb# article: http://ogp.me/ns/article#" : "og: http://ogp.me/ns# fb: http://ogp.me/ns/fb# website: http://ogp.me/ns/website#"' name='prefix'/>

    <!-- DNS Prefetch -->
    <b:include name='DNSPrefetech'/>
    <!-- Default Meta -->
    <b:include name='DefaultMeta'/>
    <!-- Title -->
    <title><b:if cond='data:view.isError'><data:blog.title.escaped/></b:if><b:if cond='data:view.isMultipleItems'><b:if cond='data:view.isHomepage'><data:blog.title.escaped/><b:else/><data:blog.pageTitle.escaped/></b:if><b:elseif cond='data:view.isSingleItem'/><data:view.title.escaped/></b:if></title>
    <!-- Open Graph -->
    <b:include name='OpenGraph'/>
    <!-- Twitter Card -->
    <b:include name='TwitterCard'/>
    <!-- Feed Links -->
    <b:eval expr='data:blog.feedLinks'/>

    <!-- Required -->
    <meta content='' property='fb:app_id'/>
    <meta content='' property='fb:admins'/>
    <b:if cond='data:view.isSingleItem'>
      <meta content='' property='article:publisher'/> 
      <meta content='' property='article:author'/>
    </b:if>

    <!-- Template Skin -->
    <b:skin><![CDATA[ 
/* === Squeeze Template ====
-> Platform : Blogger
-> Category : Magazine
-> Homepage : https://squeeze-template.blogspot.com
-> Version  : 2.3.1
-> Updated  : May 2023 
*/
/* Variables
===================
<Group description="أساسي" selector="body">
  <Variable name="keycolor" description="اللون الرئيسي" type="color" default="#d24949" value="#d24949"/>
  <Variable name="step.color" description="اللون المساعد" type="color" default="#9e44c9" value="#9e44c9"/>
  <Variable name="grad.color" description="لون الرابط (عند خلفية متدرجة)" type="color" default="#ffffff" value="#ffffff"/>
  <Variable name="body.background.color" description="لون تبويب المدونة في الهاتف" type="color"  default="#d24949" value="#d24949"/>
  <Variable name="body.background" description="الخلفية" type="background" color='transparent' default="$(color) url(https://3.bp.blogspot.com/-TTjKNu81qZc/WmZwH-ZbHiI/AAAAAAAAAIE/5AD2e-RqhbUjv2-w13HQ5o8jFF_0krT-ACK4BGAYYCw/s1600-rw-e360/SqBack.jpg) no-repeat fixed top right" value="$(color) url(https://3.bp.blogspot.com/-TTjKNu81qZc/WmZwH-ZbHiI/AAAAAAAAAIE/5AD2e-RqhbUjv2-w13HQ5o8jFF_0krT-ACK4BGAYYCw/s1600-rw-e360/SqBack.jpg) no-repeat fixed top right"/>
  <Variable name="main.back" description="لون الخلفية الداخلي" type="color" default="#ffffff" value="#ffffff"/>
  <Variable name="startSide" description="start direction" type="automatic" default="left" hideEditor="true" />
  <Variable name="endSide" description="end direction" type="automatic" default="right" hideEditor="true" />
</Group>
<Group description="الشريط العلوي" selector="#top-bar">
  <Variable name="topbar.back" description="خلفية الشريط" type="color" default="#111111" value="#111111"/>
  <Variable name="topbar.link.color" description="لون الروابط" type="color" default="#bbb" value="#bbbbbb"/>
  <Variable name="topbar.link.back" description="خلفية الروابط" type="color" default="#222222" value="#222222"/>
  <Variable name="topbar.search.color" description="لون مربع البحث" type="color" default="#fff" value="#ffffff"/>
  <Variable name="topbar.search.back" description="خلفية مربع البحث" type="color" default="#333" value="#333333"/>
</Group>
<Group description="القائمة الرئيسية" selector="#menu-bar">
  <Variable name="menu.back" description="خلفية القائمة" type="color" default="#222222" value="#222222"/>
  <Variable name="menu.link.color" description="لون الروابط" type="color" default="#bbb" value="#bbbbbb"/>
  <Variable name="menu.link.hover.color" description="لون الروابط عند المرور" type="color" default="#eee" value="#eeeeee"/>
  <Variable name="menu.link.hover.back" description="خلفية الروابط عند المرور" type="color" default="#333333" value="#333333"/>
</Group>
<Group description="الشريط المتحرك" selector="#tick">
  <Variable name="ticker.back" description="خلفية الشريط" type="color" default="#eee" value="#eeeeee"/>
  <Variable name="ticker.title.color" description="لون العنوان" type="color" default="#eee" value="#eeeeee"/>
  <Variable name="ticker.title.back" description="خلفية العنوان" type="color" default="#222" value="#222222"/>
  <Variable name="ticker.link.color" description="لون الروابط" type="color" default="#222" value="#222222"/>
</Group>
<Group description="الشريط الجانبي" selector="aside">
  <Variable name="aside.title" description="عنوان العناصر" type="color" default="#222222" value="#222222"/>
  <Variable name="aside.link" description="لون الروابط" type="color" default="#000000" value="#000000"/>
  <Variable name="aside.text" description="لون النصوص" type="color" default="#4F4F4F" value="#4F4F4F"/>
  <Variable name="aside.line" description="لون الفواصل" type="color" default="#eeeeee" value="#eeeeee"/>
</Group>
<Group description="الجانب الرئيسي" selector=".main-wrap">
  <Variable name="home.cate.title" description="عنوان العنصر" type="color" default="#222222" value="#222222"/>
  <Variable name="home.cate.link" description="لون الروابط" type="color" default="#000000" value="#000000"/>
  <Variable name="home.cate.text" description="لون النصوص" type="color" default="#4F4F4F" value="#4F4F4F"/>
  <Variable name="home.cate.line" description="لون الفواصل" type="color" default="#eeeeee" value="#eeeeee"/>
</Group>
<Group description="المشاركات" selector=".post-body">
  <Variable name="post.text" description="لون نص المشاركة" type="color" default="#222" value="#222222"/>
  <Variable name="post.headline" description="لون العناوين الجانبيه" type="color" default="#111" value="#111111"/>
  <Variable name="post.headline.back" description="خلفية العناوين الجانبيه" type="color" default="#f7f7f7" value="#f7f7f7"/>
  <Variable name="post.link.color" description="لون الروابط" type="color" default="#9e44c9" value="#9e44c9"/>
  <Variable name="post.line" description="لون الفواصل" type="color" default="#eee" value="#eeeeee"/>
  <Variable name="post.nav.back" description="خلفية الشريط الداخلي" type="color" default="#222" value="#222222"/>
  <Variable name="post.nav.color" description="لون روابط الشريط الداخلي" type="color" default="#eee" value="#eeeeee"/>
  <Variable name="post.nav.line" description="لون فواصل الشريط الداخلي" type="color" default="#eeeeee" value="#eeeeee"/>
</Group>
<Group description="الفوتر" selector="footer">
  <Variable name="footer.back1" description="تدرج الخلفية 1" type="color" default="#211837" value="#211837"/>
  <Variable name="footer.back2" description="تدرج الخلفية 2" type="color" default="#111111" value="#111111"/>
  <Variable name="footer.title" description="عنوان العناصر" type="color" default="#eeeeee" value="#eeeeee"/>
  <Variable name="footer.link" description="لون الروابط" type="color" default="#ffffff" value="#ffffff"/>
  <Variable name="footer.text" description="لون النصوص" type="color" default="#eeeeee" value="#eeeeee"/>
  <Variable name="footer.line" description="لون الفواصل" type="color" default="#2d283b" value="#2d283b"/>
</Group>
<Group description='نموذج التعليقات' selector="#comments">
  <Variable name="body.text.color" description="لون النص" type="color" default="#777777" value="#777777"/>
  <Variable name="body.link.color" description="لون الروابط" type="color" default="#9e44c9" value="#9e44c9"/>
  <Variable name="posts.background.color" description="لون الخلفية" type="color" default="#ffffff" value="#ffffff"/>
  <Variable name="posts.title.color" description="لون العنوان" type="color" default="#d24949" value="#d24949"/>
  <Variable name="posts.icons.color" description="خلفية الأيقونات" type="color" default="#9e44c9" value="#9e44c9"/>
  <Variable name="body.text.font" description="text font" type="font" default="600 14px 'Cairo', Arial" hideEditor="true" value="600 14px 'Cairo', Arial"/>
  <Variable name="tabs.font" description="tabs font" type="font" default="14px 'Cairo', Arial" hideEditor="true" value="14px 'Cairo', Arial"/>
  <Variable name="posts.text.color" description="Post text color" type="color" default="#757575" hideEditor="true" value="#757575"/> 
  <Variable name="labels.background.color" description="Label background color" type="color" default="#eeeeee" hideEditor="true" value="#eeeeee"/>
</Group>
<Group selector='body' description="ضبط العروض">
  <Variable name="content.width" description="Content width" type="length" min="640px" max="1300px" default="1200px" value="1200px"/>
  <Variable name="sidebar.width" description="Sidebar width" type="length" min="150px" max="480px" default="330px" value="330px"/>
  <Variable name="main.margin" description="المسافة الرأسية بين العناصر" type="length" min="0px" max="50px" default="20px" value="20px"/>
  <Variable name="main.padding" description="الهوامش الداخليه للعناصر" type="length" min="0px" max="50px" default="20px" value="20px"/>
</Group>
<Group selector='body' description="الوضع الليلي">
  <Variable name="dm.main" description="الخلفية الداخلية" type="color" default="#111111" value="#111111"/>
  <Variable name="dm.menu" description="خلفية القوائم" type="color" default="#1b1b1b" value="#1b1b1b"/>
  <Variable name="dm.track" description="خلفية الشرائط" type="color" default="#333333" value="#333333"/>
  <Variable name="dm.link" description="لون الروابط" type="color" default="#c7c7c7" value="#c7c7c7"/>
  <Variable name="dm.text" description="لون النصوص" type="color" default="#bbb" value="#bbbbbb"/>
  <Variable name="dm.shade" description="لون التظليل" type="color" default="#282828" value="#282828"/>
  <Variable name="dm.border" description="لون الفواصل" type="color" default="#2d283b" value="#2d283b"/>
</Group>
*/
/* Normalize
===================*/
html{font-family:serif;line-height:1.15;-ms-text-size-adjust:100%;-webkit-text-size-adjust:100%}body{margin:0}article,aside,footer,header,nav,section{display:block}h1{font-size:2em;margin:.67em 0}figcaption,figure,main{display:block}figure{margin:1em 40px}hr{box-sizing:content-box;height:0;overflow:visible}pre{font-family:monospace,monospace;font-size:1em}a{background-color:transparent;-webkit-text-decoration-skip:objects}a:active,a:hover{outline-width:0}abbr[title]{border-bottom:none;text-decoration:underline;text-decoration:underline dotted}b,strong{font-weight:inherit;font-weight:bolder}code,kbd,samp{font-family:monospace,monospace;font-size:1em}dfn{font-style:italic}mark{background-color:#ff0;color:#000}small{font-size:80%}sub,sup{font-size:75%;line-height:0;position:relative;vertical-align:baseline}sub{bottom:-.25em}sup{top:-.5em}audio,video{display:inline-block}audio:not([controls]){display:none;height:0}img{max-width:100%;border-style:none}svg:not(:root){overflow:hidden}button,input,optgroup,select,textarea{font-family:serif;font-size:100%;line-height:1.15;margin:0}button,input{important;overflow:visible}button,select{text-transform:none}button,html [type="button"],
[type="reset"],[type="submit"]{-webkit-appearance:button}button::-moz-focus-inner,[type="button"]::-moz-focus-inner,[type="reset"]::-moz-focus-inner,[type="submit"]::-moz-focus-inner{border-style:none;padding:0}button:-moz-focusring,[type="button"]:-moz-focusring,[type="reset"]:-moz-focusring,[type="submit"]:-moz-focusring{outline:1px dotted ButtonText}fieldset{border:1px solid silver;margin:0 2px;padding:.35em .625em .75em}legend{box-sizing:border-box;color:inherit;display:table;max-width:100%;padding:0;white-space:normal}progress{display:inline-block;vertical-align:baseline}textarea{overflow:auto}[type="checkbox"],[type="radio"]{box-sizing:border-box;padding:0}[type="number"]::-webkit-inner-spin-button,[type="number"]::-webkit-outer-spin-button{height:auto}[type="search"]{-webkit-appearance:textfield;outline-offset:-2px}[type="search"]::-webkit-search-cancel-button,[type="search"]::-webkit-search-decoration{-webkit-appearance:none}::-webkit-file-upload-button{-webkit-appearance:button;font:inherit}details,menu{display:block}summary{display:list-item}canvas{display:inline-block}template{display:none}[hidden]{display:none}
*,:before,:after{-webkit-box-sizing:border-box;-moz-box-sizing:border-box;box-sizing:border-box}
iframe{border:none}

/* Spinner
===================*/
.Loading{position:fixed;z-index:9999;background-color:#fff;width:100vw;height:100vh;right:0;top:0}.spinner{text-align:center;top:-webkit-calc(50% - 12.5px);top:-moz-calc(50% - 12.5px);top:calc(50% - 12.5px);position:relative}.spinner > div{margin:0 5px;width:20px;height:20px;background:-webkit-linear-gradient(to $endSide,$(keycolor),$(step.color));background:-linear-gradient(to $endSide,$(keycolor),$(step.color));background:-moz-linear-gradient(to $endSide,$(keycolor),$(step.color));background:-o-linear-gradient(to $endSide,$(keycolor),$(step.color));background:linear-gradient(to $endSide,$(keycolor),$(step.color));border-radius:100%;display:inline-block;-webkit-animation:sk-bouncedelay 1.4s infinite ease-in-out both;-moz-animation:sk-bouncedelay 1.4s infinite ease-in-out both;-o-animation:sk-bouncedelay 1.4s infinite ease-in-out both;animation:sk-bouncedelay 1.4s infinite ease-in-out both}.spinner .bounce1{-webkit-animation-delay:-.32s;-moz-animation-delay:-.32s;-o-animation-delay:-.32s;animation-delay:-.32s}.spinner .bounce2{-webkit-animation-delay:-.16s;-moz-animation-delay:-.16s;-o-animation-delay:-.16s;animation-delay:-.16s}@-webkit-keyframes sk-bouncedelay{0%,80%,100%{-webkit-transform:scale(0)}40%{-webkit-transform:scale(1.0)}}@-moz-keyframes sk-bouncedelay{0%,80%,100%{-webkit-transform:scale(0);-moz-transform:scale(0);transform:scale(0)}40%{-webkit-transform:scale(1.0);-moz-transform:scale(1.0);transform:scale(1.0)}}@-o-keyframes sk-bouncedelay{0%,80%,100%{-webkit-transform:scale(0);-o-transform:scale(0);transform:scale(0)}40%{-webkit-transform:scale(1.0);-o-transform:scale(1.0);transform:scale(1.0)}}@keyframes sk-bouncedelay{0%,80%,100%{-webkit-transform:scale(0);-moz-transform:scale(0);-o-transform:scale(0);transform:scale(0)}40%{-webkit-transform:scale(1.0);-moz-transform:scale(1.0);-o-transform:scale(1.0);transform:scale(1.0)}}

/* Fonts
===================*/
@font-face{font-family:'Cairo';font-style:normal;font-weight:400;font-display:swap;src:local(Cairo),local(Cairo-Regular),url(https://fonts.gstatic.com/s/cairo/v6/SLXGc1nY6HkvalIkTpu0xg.woff2) format("woff2");unicode-range:U+0600-06FF,U+200C-200E,U+2010-2011,U+204F,U+2E41,U+FB50-FDFF,U+FE80-FEFC}
@font-face{font-family:'Cairo';font-style:normal;font-weight:400;font-display:swap;src:local(Cairo),local(Cairo-Regular),url(https://fonts.gstatic.com/s/cairo/v6/SLXGc1nY6HkvalIvTpu0xg.woff2) format("woff2");unicode-range:U+0100-024F,U+0259,U+1E00-1EFF,U+2020,U+20A0-20AB,U+20AD-20CF,U+2113,U+2C60-2C7F,U+A720-A7FF}
@font-face{font-family:'Cairo';font-style:normal;font-weight:400;font-display:swap;src:local(Cairo),local(Cairo-Regular),url(https://fonts.gstatic.com/s/cairo/v6/SLXGc1nY6HkvalIhTps.woff2) format("woff2");unicode-range:U+0000-00FF,U+0131,U+0152-0153,U+02BB-02BC,U+02C6,U+02DA,U+02DC,U+2000-206F,U+2074,U+20AC,U+2122,U+2191,U+2193,U+2212,U+2215,U+FEFF,U+FFFD}

/* Carousel
===================*/
@keyframes splide-loading{
0%{transform:rotate(0)}
to{transform:rotate(1turn)}
}
.splide__container{position:relative;box-sizing:border-box}
.splide__list{margin:0!important;padding:0!important;width:-webkit-max-content;width:max-content;will-change:transform}
.splide.is-active .splide__list{display:flex}
.splide{visibility:hidden}
.splide,.splide__slide{position:relative}
.splide__slide{box-sizing:border-box;list-style-type:none!important;margin:0;flex-shrink:0}
.splide__slide img{vertical-align:bottom}
.splide__slider{position:relative}
.splide__track{position:relative;z-index:0;overflow:hidden}
.splide--draggable>.splide__track>.splide__list>.splide__slide{-webkit-user-select:none;user-select:none}
.splide--fade>.splide__track>.splide__list{display:block}
.splide--fade>.splide__track>.splide__list>.splide__slide{position:absolute;top:0;left:0;z-index:0;opacity:0}
.splide--fade>.splide__track>.splide__list>.splide__slide.is-active{position:relative;z-index:1;opacity:1}
.splide--rtl{direction:rtl}
.splide--ttb>.splide__track>.splide__list{display:block}
.splide__arrow svg{width:1.2em;height:1.2em;fill:currentColor}
.splide__arrow{cursor:pointer}
.splide__arrow--prev svg{transform:scaleX(-1)}
.splide--nav>.splide__track>.splide__list>.splide__slide:focus{outline:none}
.splide--rtl>.splide__arrows .splide__arrow--prev svg,.splide--rtl>.splide__track>.splide__arrows .splide__arrow--prev svg{transform:scaleX(1)}
.splide--rtl>.splide__arrows .splide__arrow--next svg,.splide--rtl>.splide__track>.splide__arrows .splide__arrow--next svg{transform:scaleX(-1)}
.splide--ttb>.splide__arrows .splide__arrow--prev svg,.splide--ttb>.splide__track>.splide__arrows .splide__arrow--prev svg{transform:rotate(-90deg)}
.splide--ttb>.splide__arrows .splide__arrow--next svg,.splide--ttb>.splide__track>.splide__arrows .splide__arrow--next svg{transform:rotate(90deg)}

/* Main
===================*/
body{font-family:'Cairo',sans-serif;font-size:14px;line-height:1.5em;visibility:visible!important}
body[data-overflow='false']{overflow:hidden}
body.boxed .main-container{max-width:$(content.width)}
body[data-protect='true']{-webkit-user-select:none;-khtml-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}
body::-webkit-scrollbar{background-color:$(main.back);width:10px}
body::-webkit-scrollbar-thumb{background:$(keycolor)}
body::-moz-selection{background:$(keycolor);color:#FFF}
::-moz-selection{background:$(keycolor);color:#FFF}
::selection{background:$(keycolor);color:#FFF}
ul{list-style:none;padding:0;margin:0}
p{line-height:2;font-size:12px}
a{text-decoration:none;color:inherit}
.main-container{max-width:100%;width:100%;margin:0 auto;background-color:$(main.back);-webkit-box-shadow:0 4px 8px 0 rgba(0,0,0,0.2),0 6px 20px 0 rgba(0,0,0,0.19);-moz-box-shadow:0 4px 8px 0 rgba(0,0,0,0.2),0 6px 20px 0 rgba(0,0,0,0.19);box-shadow:0 4px 8px 0 rgba(0,0,0,0.2),0 6px 20px 0 rgba(0,0,0,0.19)}
.main-container:before,.main-container:after{content:'';display:block;height:3px;background:-webkit-linear-gradient(to $endSide,$(keycolor),$(step.color));background:-o-linear-gradient(to $endSide,$(keycolor),$(step.color));background:-moz-linear-gradient(to $endSide,$(keycolor),$(step.color));background:linear-gradient(to $endSide,$(keycolor),$(step.color))}
#RecentPosts{margin-bottom:$(main.margin)}
.side-$startSide{margin-bottom:$(main.margin)}
body.no-sidebar .side-$startSide{float:none;width:100%}
body.no-sidebar aside{display:none}
.tempscheme{border-color:$(keycolor) $(footer.line) $(step.color) $(footer.text);float:$startSide;text-align:$endSide}

/* Widths
===================*/
.middle-content{display:flex;align-items:flex-start;justify-content:space-between}
.side-$startSide{position:relative;width:calc(100% - $(sidebar.width) - 20px)}
aside{width:$(sidebar.width);position:relative;margin-$startSide:$(main.padding)}
.wrapper{max-width:$(content.width);margin:0 auto}
#footer .color-wrap{position:relative;background-color:rgba(0,0,0,0.1)}
.main-wrap{padding:0 $(main.padding)}
.stickysides .side-right,.stickysides aside{position:sticky;top:0}

/* Framework
===================*/
/* Headlines */
.headline{margin-bottom:15px;border-bottom:2px solid $(home.cate.line)}
.main-wrap aside .headline{margin-bottom:15px;border-bottom:2px solid $(aside.line)}
footer .headline{margin-bottom:15px;border-bottom:2px solid $(footer.line)}
.headline h2,.headline h4{display:inline-block;margin:0 0 12px;font-size:17px;position:relative}
.main-wrap .headline h2{color:$(home.cate.title)}
.main-wrap .headline h4{color:$(home.cate.title)}
.main-wrap .headline{border-bottom:2px solid $(post.line)}
.main-wrap aside .headline h4{color:$(aside.title)}
footer .headline h4{color:$(footer.title)}
.headline h2:after,.headline h4:after{content:"";height:2px;background:-webkit-linear-gradient(to $endSide,$(keycolor),$(step.color));background:-o-linear-gradient(to $endSide,$(keycolor),$(step.color));background:-moz-linear-gradient(to $endSide,$(keycolor),$(step.color));background:linear-gradient(to $endSide,$(keycolor),$(step.color));position:absolute;top:33px;$endSide:0;$startSide:0;bottom:0}
.headline > a{color:$(home.cate.text);float:$endSide;padding:5px 12px;font-size:12px;background-color:$(home.cate.line);border-radius:5px;line-height:18px}

/* Read More */
.read-more{display:inline-block;background:-webkit-linear-gradient(to $endSide,$(keycolor),$(step.color));background:-o-linear-gradient(to $endSide,$(keycolor),$(step.color));background:-moz-linear-gradient(to $endSide,$(keycolor),$(step.color));background:linear-gradient(to $endSide,$(keycolor),$(step.color));padding:5px 15px;font-size:14px;font-weight:700;color:$(grad.color);border-radius:30px}
.read-more:hover{padding:5px 25px}

/* Post Share */
.post-share{float:$endSide}
.post-share .share-icon{z-index:1;float:$endSide;width:31px;height:31px;line-height:31px;text-align:center;background:-webkit-linear-gradient(to $endSide,$(keycolor),$(step.color));background:-o-linear-gradient(to $endSide,$(keycolor),$(step.color));background:-moz-linear-gradient(to $endSide,$(keycolor),$(step.color));background:linear-gradient(to $endSide,$(keycolor),$(step.color));color:$(grad.color);font-size:16px;border-radius:100%;cursor:pointer;position:relative}
.post-share .share-icon:after{opacity:0;content:"";font-family:FontAwesome;border-width:5px;border-style:solid;position:absolute;top:11px;$endSide:97%;pointer-events:none}
.rtl .post-share .share-icon:after{border-color:transparent transparent transparent $(keycolor)}
.ltr .post-share .share-icon:after{border-color:transparent $(keycolor) transparent transparent}
.post-share .share-icon.arrow:after{opacity:1}
.post-share .share-icon i{pointer-events:none}
.post-share .share-menu{visibility:hidden;float:$endSide;margin-top:1px;margin-bottom:0;margin-$endSide:5px}
.post-share .share-menu li{float:$endSide;opacity:0;margin-$endSide:5px}
.rtl .post-share .share-menu li{-webkit-transform:translateX(-30px);-ms-transform:translateX(-30px);-moz-transform:translateX(-30px);-o-transform:translateX(-30px);transform:translateX(-30px)}
.ltr .post-share .share-menu li{-webkit-transform:translateX(30px);-ms-transform:translateX(30px);-moz-transform:translateX(30px);-o-transform:translateX(30px);transform:translateX(30px)}
.post-share .share-menu li a:before{width:27px;height:27px;line-height:29px;margin-top:1px;border-radius:100%;color:#FFF;display:block;font-family:FontAwesome;text-align:center;}
.post-share .share-menu li:nth-of-type(1) a:before{content:"\f09a";background-color:#3b5998}
.post-share .share-menu li:nth-of-type(2) a:before{content:"\f099";background-color:#1da1f2}
.post-share .share-menu li:nth-of-type(3) a:before{content:"\f231";background-color:#cc2127}
.share-open.share-menu{visibility:visible}
[dir] .share-open.share-menu li{opacity:1;-webkit-transform:translateX(0);-ms-transform:translateX(0);-moz-transform:translateX(0);-o-transform:translateX(0);transform:translateX(0)}
.share-open.share-menu li:nth-of-type(1){-webkit-transition:.4s linear;-o-transition:.4s linear;-moz-transition:.4s linear;transition:.4s linear}
.share-open.share-menu li:nth-of-type(2){-webkit-transition:.3s linear;-o-transition:.3s linear;-moz-transition:.3s linear;transition:.3s linear}
.share-open.share-menu li:nth-of-type(3){-webkit-transition:.2s linear;-o-transition:.2s linear;-moz-transition:.2s linear;transition:.2s linear}

/* Image Wrap */
.img-wrap{display:block;border-radius:5px;overflow:hidden;position:relative}
.img-wrap:hover img{-webkit-transform:scale(1.1) rotate(3deg);-ms-transform:scale(1.1) rotate(3deg);-moz-transform:scale(1.1) rotate(3deg);-o-transform:scale(1.1) rotate(3deg);transform:scale(1.1) rotate(3deg)}
.overlay{opacity:0;width:100%;height:100%;overflow:hidden;border-radius:5px;background:-webkit-linear-gradient(to $endSide,$(keycolor),$(step.color));background:-o-linear-gradient(to $endSide,$(keycolor),$(step.color));background:-moz-linear-gradient(to $endSide,$(keycolor),$(step.color));background:linear-gradient(to $endSide,$(keycolor),$(step.color));position:absolute;top:0;$endSide:0}{opacity:0;width:100%;height:100%;overflow:hidden;border-radius:5px;background:rgba(0,0,0,0.6);position:absolute;top:0;$endSide:0}
.img-wrap:hover .overlay{opacity:.8}
.details-on-img{position:absolute;top:0;$endSide:0;$startSide:0;bottom:0}
.details-on-img .author-prof,.details-on-img .post-date{padding:0 4px;background:$(main.back);font-size:10px;border-radius:2px;position:absolute;$startSide:-100%;z-index:2;-webkit-box-shadow:-2px 2px 10px -1px rgba(0,0,0,0.3);-moz-box-shadow:-2px 2px 10px -1px rgba(0,0,0,0.3);box-shadow:-2px 2px 10px -1px rgba(0,0,0,0.3)}
.details-on-img .author-prof{color:$(keycolor)}
.details-on-img .post-date{color:$(step.color)}
.img-wrap .author-prof{top:30px;-webkit-transition:.4s linear;-o-transition:.4s linear;-moz-transition:.4s linear;transition:.4s linear}
.img-wrap .post-date{top:55px;-webkit-transition:.6s linear;-o-transition:.6s linear;-moz-transition:.6s linear;transition:.6s linear}
.img-wrap:hover .author-prof,.img-wrap:hover .post-date{$startSide:0}
.img-wrap img{display:block;width:100%;height:100%}
.rtl .details-on-img > *{direction:rtl}
.ltr .details-on-img > *{direction:ltr}
.details-on-img i{margin-$endSide:5px;float:$startSide;margin-top:5px}
.caption{padding:30px;background:-webkit-gradient(linear,left top, left bottom,from(transparent),color-stop(rgba(0,0,0,0.7)),to(#000));background:-webkit-linear-gradient(transparent,rgba(0,0,0,0.7),#000);background:-o-linear-gradient(transparent,rgba(0,0,0,0.7),#000);background:-moz-linear-gradient(transparent,rgba(0,0,0,0.7),#000);background:linear-gradient(transparent,rgba(0,0,0,0.7),#000);color:#FFF;position:absolute;bottom:0;$startSide:0;$endSide:0}
aside .img-wrap:before,aside .img-wrap:after{display:none}

/* Social Colors */
.social .fa-facebook{background-color:#3b5998}.social .fa-twitter{background-color:#1da1f2}.social .fa-rss{background-color:#f26522}.social .fa-dribbble{background-color:#ea4c89}.social .fa-google-plus{background-color:#dd4b39}.social .fa-pinterest{background-color:#cc2127}.social .fa-linkedin{background-color:#0976b4}.social .fa-wordpress{background-color:#00769d}.social .fa-github{background-color:#000000}.social .fa-youtube{background-color:#e52d27}.social .fa-quora{background-color:#a82400}.social .fa-spotify{background-color:#1ed760}.social .fa-snapchat{background-color:#f5d602}.social .fa-flickr{background-color:#FF0084}.social .fa-instagram{background-color:#7c38af;background:radial-gradient(circle at 0 130%, #fdf497 0%, #fdf497 5%, #fd5949 45%,#d6249f 60%,#285AEB 90%)}.social .fa-behance{background-color:#009fff}.social .fa-whatsapp{background-color:#0D660A}.social .fa-soundcloud{background-color:#FF5419}.social .fa-tumblr{background-color:#3e5a70}.social .fa-khamsat{background-color:#f9b01c}.social .fa-tradent{background-color:#59c5c4}.social .fa-blogger{background-color:#fc9644}.social .fa-telegram{background-color:#32AEE1}.social .fa-google-play{background-color:#3d9dab}.social .fa-mostaql{background-color:#2caae2}.social .fa-messenger{background-color:#0084ff}.social .fa-paypal{background-color:#193685}.social .fa-reddit{background-color:#ff4500}.social .fa-vk{background-color:#45668e}.social .fa-website{background-color:#444444}.fa-website:before{content:"\f0ac"}social .fa-tiktok{background-color:#000}.social .fa-discord{background-color:#6265ed}.social .fa-patreon{background-color:#f74a20}.social .fa-twitch{background-color:#a970ff}.social .fa-tiktok{background-color:#000}

/* Label-Title */
.label-title{padding:0 8px;background:-webkit-linear-gradient(to $endSide,$(keycolor),$(step.color));background:-o-linear-gradient(to $endSide,$(keycolor),$(step.color));background:-moz-linear-gradient(to $endSide,$(keycolor),$(step.color));background:linear-gradient(to $endSide,$(keycolor),$(step.color));color:$(grad.color);font-size:12px;border-radius:10px;position:absolute;top:10px;$startSide:10px;z-index:2}
.img-wrap:hover .label-title{$startSide:-100%}
.img-wrap .label-name{float:$startSide;background:-webkit-linear-gradient(to $endSide,$(keycolor),$(step.color));background:-o-linear-gradient(to $endSide,$(keycolor),$(step.color));background:-moz-linear-gradient(to $endSide,$(keycolor),$(step.color));background:linear-gradient(to $endSide,$(keycolor),$(step.color));padding:2px 10px;border-radius:100px;font-size:12px;margin-bottom:10px;color:$(grad.color);position:relative;$startSide:0}
.img-wrap:hover .label-name{$startSide:-100%}

/* Ribble Button */
.ribble{position:relative;overflow:hidden}
.ribble span{font-weight:bold;position:relative;z-index:1;-webkit-transition:.6s ease-in-out;-o-transition:.6s ease-in-out;-moz-transition:.6s ease-in-out;transition:.6s ease-in-out}
.ribble:hover{padding:5px 20px}
.ribble:hover span{color:$(grad.color)}
.ribble:before{content:'';background:-webkit-linear-gradient(to $endSide,$(keycolor),$(step.color));background:-o-linear-gradient(to $endSide,$(keycolor),$(step.color));background:-moz-linear-gradient(to $endSide,$(keycolor),$(step.color));background:linear-gradient(to $endSide,$(keycolor),$(step.color));position:absolute;top:100%;$startSide:-200%;height:200%;width:200%;display:block;z-index:1;border-radius:100px;-webkit-transform:scale(0);-ms-transform:scale(0);-moz-transform:scale(0);-o-transform:scale(0);transform:scale(0);-webkit-transition:.5s ease-in-out;-o-transition:.5s ease-in-out;-moz-transition:.5s ease-in-out;transition:.5s ease-in-out}
.ribble:hover:before{top:-25px;$startSide:-50px;-webkit-transform:scale(1);-ms-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1)}

/* Other */
.blog-admin,#uds-searchControl,#ContactForm93{display:none}
.clear-left{display:block;clear:$endSide}
.clear{clear:both;display:block}
object{max-width:100%}
.hide{display:none!important}
*:not(.notr),:not(.notr):before,:not(.notr):after{-webkit-transition:.3s ease-in-out;-o-transition:.3s ease-in-out;-moz-transition:.3s ease-in-out;transition:.3s ease-in-out}
.post-body #ContactForm93{display:block}
div#Tempnec{display:none!important}
#blogger-components{display:none!important}
body .cookie-choices-info{top:auto;bottom:0;background-color:#333}

/* Header
===================*/
header .color-wrap{background-color:$(topbar.back)}
header #top-bar{padding:0 $(main.padding);height:40px;line-height:40px;color:#FFF;position:relative}
#head-sec{padding:$(main.margin) $(main.padding);min-height:120px;overflow:hidden}

/* Fixed TopBar */
header .color-wrap.fixed{position:fixed;width:100%;top:0;$startSide:0;z-index:6}

/* Header Social */
header #top-bar #LinkList301{z-index:1;max-width:30%;margin-$endSide:45px;float:$endSide;position:relative;height:40px}
#LinkList301 .social-static{margin-top:5px;white-space:nowrap;overflow-x:auto}
#LinkList301 .social-static li{vertical-align:top}
#LinkList301 .social-static::-webkit-scrollbar{$(main.back);width:0px}
.social-static li{display:inline-block;margin:1px 2px 0;overflow:hidden}
.social-static li i{color:#FFF;width:28px;height:28px;line-height:28px;border-radius:100%;display:block;text-align:center;font-size:16px}
.social-static li svg{width:28px;height:28px;padding:6px 0;border-radius:100%;display:block;fill:#FFF}
.social-static li:hover{-webkit-animation:SocIcons .2s ease-in-out;-moz-animation:SocIcons .2s ease-in-out;-o-animation:SocIcons .2s ease-in-out;animation:SocIcons .2s ease-in-out}
@-webkit-keyframes SocIcons{
50%{-webkit-transform:scale(0.7);transform:scale(0.7)}
100%{-webkit-transform:scale(1.5);transform:cale(1.5)}
}
@-moz-keyframes SocIcons{
50%{-webkit-transform:scale(0.7);-moz-transform:scale(0.7);transform:scale(0.7)}
100%{-webkit-transform:scale(1.5);-moz-transform:cale(1.5);transform:cale(1.5)}
}
@-o-keyframes SocIcons{
50%{-webkit-transform:scale(0.7);-o-transform:scale(0.7);transform:scale(0.7)}
100%{-webkit-transform:scale(1.5);-o-transform:cale(1.5);transform:cale(1.5)}
}
@keyframes SocIcons{
50%{-webkit-transform:scale(0.7);-moz-transform:scale(0.7);-o-transform:scale(0.7);transform:scale(0.7)}
100%{-webkit-transform:scale(1.5);-moz-transform:cale(1.5);-o-transform:cale(1.5);transform:cale(1.5)}
}

/* Header Pages List */
header #top-bar #PageList301{float:$startSide;height:40px}
header #top-bar .menu li{float:$startSide;margin:8px 0}
header #top-bar .menu li a{-webkit-transition:none;-o-transition:none;-moz-transition:none;transition:none;background:$(topbar.link.back);margin-$endSide:7px;font-size:12px;display:block;line-height:normal;padding:0 10px;border-radius:5px;font-weight:700;color:$(topbar.link.color);line-height:2em}
header #top-bar .menu li a:hover,header #top-bar .menu li.selected a{color:$(grad.color)!important;background:-webkit-linear-gradient(to $endSide,$(keycolor),$(step.color));background:-o-linear-gradient(to $endSide,$(keycolor),$(step.color));background:-moz-linear-gradient(to $endSide,$(keycolor),$(step.color));background:linear-gradient(to $endSide,$(keycolor),$(step.color))}
header #top-bar .menu-res{display:none}
header #top-bar .menu-res button{background:none;border:none;display:block;width:30px;height:30px;line-height:25px;padding:0;font-size:18px;background:-webkit-linear-gradient(to $endSide,$(keycolor),$(step.color));background:-o-linear-gradient(to $endSide,$(keycolor),$(step.color));background:-moz-linear-gradient(to $endSide,$(keycolor),$(step.color));background:linear-gradient(to $endSide,$(keycolor),$(step.color));color:$(grad.color);text-align:center;border-radius:5px;cursor:pointer;position:absolute;top:5px;$startSide:$(main.padding);z-index:2;cursor:pointer}
.menu-res-wrap ul:before{content:"";border-width:8px;border-style:solid;border-color:transparent transparent $(menu.back);position:absolute;top:-14px;$startSide:10px;z-index:2}
.menu-res-wrap ul{width:180px;top:50px;position:absolute;background-color:$(menu.back);$startSide:10px;padding:10px;border-radius:5px;z-index:-1;opacity:0;pointer-events:none;}
.menu-res-wrap ul.open{z-index:9999999;opacity:1;pointer-events:auto;}
.menu-res-wrap li a{display:block;border-bottom:1px dashed rgba(255,255,255,0.075);color:#eeeeee;text-align:center}
.menu-res-wrap li:last-of-type a{border-bottom:none}

/* Search Box */
header #HTML301{height:100%;position:absolute;$endSide:20px;min-width:230px}
header .search{position:relative;display:flex;justify-content:flex-end;align-items:center;height:30px;margin-top:5px}
header .search label{width:0px;overflow:hidden;height:100%;display:flex;flex-wrap:wrap}
header .search input{z-index:1;font-family:inherit;border-radius:5px;height:100%;width:100%;background-color:$(topbar.search.back);color:$(topbar.search.color);font-size:12px;border:none;padding:0 15px;outline:none}
header .search button{color:$(grad.color);background:-webkit-linear-gradient(to $endSide,$(keycolor),$(step.color));background:-o-linear-gradient(to $endSide,$(keycolor),$(step.color));background:-moz-linear-gradient(to $endSide,$(keycolor),$(step.color));background:linear-gradient(to $endSide,$(keycolor),$(step.color));display:block;width:30px;height:30px;line-height:30px;padding:0;font-size:15px;text-align:center;border-radius:5px;cursor:pointer;flex-shrink:0;position:relative;border:none}
header .search.open-search label{width:100%}

/* Logo & AD */
#Header1{width:290px;float:$startSide}
#Header1 .headone{font-weight:700;display:block;margin:0 0 10px;font-size:35px;line-height:1em;text-align:center}
#Header1 p{margin:0;font-size:12px;text-align:center;line-height:1.5em}
#Header1 img{width:auto;max-width:100%;margin:0 auto;display:block;height:auto}
.img-logo{display:block}
#HTML302,#HTML307{width:728px;float:$endSide;text-align:$endSide}

/* Main Menu */
#menu-bar{clear:both;position:relative;padding:0 $(main.padding);margin-bottom:$(main.margin)}
#LinkList302{height:58px;background-color:$(menu.back);position:relative;border-radius:5px}
#LinkList302:before{content:'';display:block;height:3px;background:-webkit-linear-gradient(to $endSide,$(keycolor),$(step.color));background:-o-linear-gradient(to $endSide,$(keycolor),$(step.color));background:-moz-linear-gradient(to $endSide,$(keycolor),$(step.color));background:linear-gradient(to $endSide,$(keycolor),$(step.color));position:absolute;width:100%;bottom:0;$startSide:0}
#menu-bar .menu-bar ul li{float:$startSide}
#menu-bar .menu-bar ul li>a{font-weight:700;display:block;width:100%;padding:17px 15px;color:$(menu.link.color);position:relative}
#menu-bar .menu-bar li>a:hover{background-color:$(menu.link.hover.back);color:$(menu.link.hover.color)}
#menu-bar .menu-bar ul li.drop-menu-st >a{padding-$endSide:30px}
.menu-bar ul i{font-size:18px;display:inline-block;vertical-align:middle;margin-$endSide:10px}
#menu-bar .home{white-space:nowrap;overflow:hidden;height:55px;line-height:25px;background:-webkit-linear-gradient(to $endSide,$(keycolor),$(step.color));background:-o-linear-gradient(to $endSide,$(keycolor),$(step.color));background:-moz-linear-gradient(to $endSide,$(keycolor),$(step.color));background:linear-gradient(to $endSide,$(keycolor),$(step.color));color:#FFF!important;min-width:65px;max-width:65px;border-start-start-radius:5px}
#menu-bar .home:hover{max-width:100%!important;padding-$startSide:40px}
#menu-bar .home span{position:relative;$startSide:50px}
#menu-bar .home:hover span{$startSide:0px}
.home:before{font-weight:400;content:"\f015";font-family:"fontawesome";font-size:20px;position:absolute;$startSide:23px;text-indent:0;-webkit-transform:scale(2);-ms-transform:scale(2);-moz-transform:scale(2);-o-transform:scale(2);transform:scale(2);top:16px}
#menu-bar .home:hover:before{$startSide:10px;-webkit-transform:scale(1);-ms-transform:scale(1);-moz-transform:scale(1);-o-transform:scale(1);transform:scale(1)}

/* Sub Menu */
#menu-bar .menu-bar li>ul li a{padding:12px 20px}
#menu-bar .menu-bar li>ul li a:hover{background:-webkit-linear-gradient(to $endSide,$(keycolor),$(step.color));background:-o-linear-gradient(to $endSide,$(keycolor),$(step.color));background:-moz-linear-gradient(to $endSide,$(keycolor),$(step.color));background:linear-gradient(to $endSide,$(keycolor),$(step.color));color:$(grad.color);padding-$startSide:30px}
.drop-menu-st{position:relative;padding-bottom:5px}
.drop-menu-st:after{content:"\f078";font-family:"fontawesome";font-size:10px;color:$(menu.link.color);position:absolute;top:18px;$endSide:10px}
.drop-menu-st > ul{display:none;width:200px;height:auto;background-color:$(menu.back);position:absolute;top:58px;z-index:10;-webkit-box-shadow:0 5px 5px 0 rgba(0,0,0,0.2);-moz-box-shadow:0 5px 5px 0 rgba(0,0,0,0.2);box-shadow:0 5px 5px 0 rgba(0,0,0,0.2)}
.drop-menu-st:hover > ul{display:block}
#menu-bar .drop-menu-st ul li{float:none;position:relative}

/* Bottom Menu */
.bot-menu-st:hover > ul{display:block}
.bot-menu-st > ul{display:none;width:200px;height:auto;background-color:$(menu.back);position:absolute;top:0;$startSide:100%;z-index:10;-webkit-box-shadow:0 5px 5px 0 rgba(0,0,0,0.2);box-shadow:0 5px 5px 0 rgba(0,0,0,0.2)}
.rtl .bot-menu-st:after{content:"\f053"}
.ltr .bot-menu-st:after{content:"\f054"}
.bot-menu-st:after{font-family:"fontawesome";font-size:10px;color:$(menu.link.color);position:absolute;top:12px;$endSide:10px}

/* Fixed Menu */
.fixed#menu-bar{height:58px}
.fixed#menu-bar #LinkList302{width:100%;position:fixed;z-index:6;top:0;$startSide:0;-webkit-box-shadow:rgba(0,0,0,0.1) 0 2px 2px;box-shadow:rgba(0,0,0,0.1) 0 2px 2px;opacity:.99;-webkit-animation:fxd 1s;animation:fxd 1s}
@-webkit-keyframes fxd{
from{top:-60px}
to{top:0}
}
@keyframes fxd{
from{top:-60px}
to{top:0}
}

/* Mega Menu */
.mega-wrap{background-color:$(menu.back);overflow:hidden;width:100%;position:absolute;top:100%;right:0;padding:0 20px;z-index:-1;max-height:0}
.mega-wrap.open{max-height:295px;height:295px;padding:20px;z-index:5}
.mega-wrap > i.fa-spin{color:$(main.back);display:block;text-align:center;font-size:40px;margin-top:80px}
.mega-post:last-of-type{margin-left:0}
.mega-post{padding:0 10px}
.mega-wrap .splide__track{margin:0 -10px}
.mega-post .img-wrap{height:180px}
.mega-post .post-title a{color:$(menu.link.color);font-size:14px}
.mega-post .post-title a:hover{color:$(menu.link.hover.color)}
.mega-post .post-title{margin:0;max-height:50px;overflow:hidden}
.mega-carousel .splide__arrows button{position:absolute;top:80px;background-color:#d8d8d8;font-family:"fontawesome";width:25px;height:25px;fill:$(menu.back);font-size:10px;line-height:0;border:none;border-radius:100%;z-index:2}
.mega-carousel .splide__arrow.splide__arrow--prev{$startSide:10px}
.mega-carousel .splide__arrow.splide__arrow--next{$endSide:10px}
.mega-post .details{margin-bottom:5px}
.mega-post .post-date i{font-size:inherit}
.mega-post .post-date{color:$(menu.link.color)}
.mega-post .details{margin:5px 0 0}

/* Responsive Menu */
.menu-bar-res{display:none;float:$endSide;border:none;background:none;padding:0px}
.menu-bar-res .fa-bars{cursor:pointer;color:$(grad.color);background:-webkit-linear-gradient(to $endSide,$(keycolor),$(step.color));background:-o-linear-gradient(to $endSide,$(keycolor),$(step.color));background:-moz-linear-gradient(to $endSide,$(keycolor),$(step.color));background:linear-gradient(to $endSide,$(keycolor),$(step.color));height:55px;width:55px;text-align:center;font-size:26px;padding-top:16px;border-start-end-radius:5px}
.res-home{display:none;float:$startSide;height:55px;width:110px;text-align:center;padding-top:17px;color:$(grad.color);background:-webkit-linear-gradient(to $endSide,$(keycolor),$(step.color));background:-o-linear-gradient(to $endSide,$(keycolor),$(step.color));background:-moz-linear-gradient(to $endSide,$(keycolor),$(step.color));background:linear-gradient(to $endSide,$(keycolor),$(step.color));font-weight:700;border-start-start-radius:5px}
.res-home:before{content:"\f015";font-family:FontAwesome;font-weight:400;font-size:24px;margin-$endSide:5px;display:inline-block;vertical-align:-2px}

/* Intro
===================*/
.intro{clear:both;padding:0 $(main.padding)}
#section11:not(:empty){margin-bottom:20px;overflow:hidden}

/* Ticker */
.ticker{height:40px;overflow:hidden;background-color:$(ticker.back);border-radius:5px}
.ticker-title{float:$startSide;height:40px;line-height:40px;padding:0 20px 0 50px;padding-top:0;padding-bottom:0px;padding-$startSide:50px;padding-$endSide:20px;background-color:$(ticker.title.back);color:$(ticker.title.color);font-size:16px;border-$endSide:4px solid $(keycolor);position:relative;z-index:1;border-radius:5px}
.ticker-title:after{content:"\f1ea";font-family:FontAwesome;font-size:18px;color:inherit;position:absolute;top:2px;$startSide:15px}
.ticker-content nav{display:flex}
.ticker-content ul{display:flex;align-items:center;height:40px;line-height:40px;position:relative}
.ticker-content li{margin:0 20px;flex-shrink:0}
.ticker-content li:first-child{margin-$startSide:0}
.ticker-content li:last-child{margin-$endSide:0}
.ticker-content li:hover{color:$(keycolor)}
.ticker-content li:before{content:"\f02e";font-family:FontAwesome;font-size:12px;color:$(keycolor);margin-$endSide:15px;display:inline-block;}
.ticker-content li a{color:$(ticker.link.color);font-weight:600;}
.ticker-content li a:hover{text-decoration:underline;color:$(keycolor)}

/* Intro Slider */
.intro .section{margin-bottom:$(main.margin)}
.main-slider .splide__track,.main-slider .splide__list{height:100%}
.main-slider .m-slider{float:$startSide;width:62.5%;height:440px}
.main-slider .m-slider .img-wrap{width:100%;height:100%}
.main-slider .m-slider .item{width:100%;height:100%}
.main-slider .left-box{float:$endSide;width:36.459%;height:440px;overflow:hidden}
.main-slider .left-box .top,.main-slider .left-box .bottom{position:relative}
.main-slider .left-box .top{margin-bottom:12px}
.main-slider .left-box .img-wrap{width:100%;height:214px}
.main-slider .img-wrap:hover .details-on-img .author-prof{top:30px}
.main-slider .img-wrap:hover .details-on-img .post-date{top:55px}
.main-slider h3{margin:0;clear:both}
.main-slider .m-slider h3{font-size:18px}
.main-slider .left-box h3{font-size:16px}
.main-slider h3 a{display:block}
.main-slider .m-slider .caption p{height:52px;overflow:hidden;margin:5px 0 0;font-size:13px}
.main-slider .splide__arrows{margin:auto;position:absolute;top:46%;left:0;right:0;z-index:1}
.main-slider .splide__arrows button.splide__arrow--next,.main-slider .splide__arrows button.splide__arrow--prev{padding:0;border:none;background:-webkit-gradient(linear,$endSide,from($(keycolor)),to($(step.color)));background:linear-gradient(to $endSide,$(keycolor),$(step.color));width:35px;height:35px;position:absolute;text-align:center;color:$(grad.color)}
.main-slider .splide__arrows .splide__arrow--next{$endSide:0;border-end-start-radius:5px;border-start-start-radius:5px}
.main-slider .splide__arrows .splide__arrow--prev{$startSide:0;border-start-end-radius:5px;border-end-end-radius:5px}
.main-slider .img-wrap{cursor:pointer}

/* Sidebar
===================*/
aside .widget{margin-bottom:$(main.margin)}

/* Footer
===================*/
#footer{position:relative;background:-webkit-gradient(linear,$endSide,from($(footer.back1)),to($(footer.back2)));background:linear-gradient(to $endSide,$(footer.back1),$(footer.back2));border-top:3px solid $(keycolor);clear:both}
#footer-sections{font-size:0;padding:$(main.margin) 10px;display:flex;justify-content:space-between}
#footer-sections .f-sec.no-items{display:none}
#footer-sections .f-sec{font-size:14px;width:100%;min-width:25%;padding:0 10px}
#footer-sections .f-sec .widget{margin-bottom:$(main.margin)}
#footer-sections .f-sec .widget:last-of-type{margin-bottom:0}
body.boxed#footer-sections{margin:0 5px}
body:not(.boxed)#footer-sections{margin:0 auto}
#footer-top-section:not(.no-items){padding:$(main.margin) 0;margin:0 $(main.padding);border-bottom:1px solid $(footer.line)}
#footer-bottom-section:not(.no-items){padding:$(main.margin) 0;margin:0 $(main.padding);border-top:1px solid $(footer.line)}
body:not(.boxed) #footer-top-section:not(.no-items).wrapper{margin:$(main.margin) auto 0;padding:0 $(main.padding) $(main.margin)}
body:not(.boxed) #footer-bottom-section:not(.no-items).wrapper{margin:0 auto;padding:$(main.margin) $(main.padding)}
#footer-top-section:not(.no-items).wrapper{padding:$(main.margin) 0;margin:0 $(main.padding)}
#footer-cop-section{padding:5px $(main.padding);position:relative}
#footer-cop-section:after{content:"";clear:both;display:block}

/* Copyrights */
#HTML303{color:$(footer.link);margin-top:5px;float:$startSide}
#HTML303 > *{vertical-align:middle;display:inline-block}
#HTML303 a{color:$(keycolor)}
#HTML303 a:hover{color:$(step.color);text-decoration:underline}
#HTML303 > b{font-family:Tahoma;color:$(keycolor);font-size:15px;margin:0 5px}
#LinkList304{float:$endSide;margin-top:5px}
.credits span{margin-$endSide:5px}

/* Footer Scroll To Top */
.scroll-top{background-color:$(footer.line);width:30px;height:30px;padding:0;text-align:center;border-radius:100px;color:$(footer.text);border:none;cursor:pointer;position:absolute;left:calc(50% - 15px);top:-15px;z-index:1}
.scroll-top i{pointer-events:none}
.scroll-top:before{display:block}
.scroll-top:hover{background:$(keycolor);color:#FFF}

/* Widgets
===================*/
.widget{position:relative}
.widget-item-control{position:absolute;$endSide:0;top:100%;z-index:2;opacity:.7}
.widget-item-control:hover{opacity:1}
#top-bar .widget-item-control{top:0}
.headline[data-title*="[SOC]"],.headline[data-title*="[ACC]"]{display:none}
.PLHolder{opacity:0.5;background:-webkit-gradient(linear,$endSide,from($(keycolor)),to($(step.color)));background:linear-gradient(to $endSide,$(keycolor),$(step.color))}
.PLHolder img{visibility:hidden}
.img-wrap img{transition:.5s all}
.pl-fade .PLHolder img{opacity:0}
.pl-zoomin .PLHolder img{transform:scale(0)}
.pl-zoomout .PLHolder img{transform:scale(2)}
.pl-rotate .PLHolder img{transform:rotate(-360deg)}
.pl-blur .PLHolder img{filter:blur(50px)}

/* Email Subscription && BlogSearch */
aside .subscrib-sec p{margin:0 0 10px;color:$(aside.text);text-align:$startSide}
footer .subscrib-sec label{display:block;font-size:12px;margin:0 0 10px;color:$(footer.text);text-align:$startSide}
aside .subscrib-sec label{color:$(aside.text)}
footer .subscrib-sec label{color:$(footer.text)}
.subscrib-sec input[name="email"],.BlogSearch .search-input input{display:block;width:100%;padding:15px;margin:auto;line-height:0;outline:0;font-size:14px;border:0;border-radius:5px;direction:ltr;text-align:left;margin-top:15px;font-family:inherit}
aside .subscrib-sec input[name="email"],aside .BlogSearch .search-input input{background-color:$(aside.line);color:$(aside.link)}
footer .subscrib-sec input[name="email"],footer .BlogSearch .search-input input{background-color:$(footer.line);color:$(footer.link)}
.msg-send,.BlogSearch .search-action{font-family:inherit;display:block;padding:10px 30px 10px 20px;margin:5px auto 0;outline:0;border:0;border-radius:5px;background:-webkit-gradient(linear,$endSide,from($(keycolor)),to($(step.color)));background:linear-gradient(to $endSide,$(keycolor),$(step.color));color:$(grad.color);font-weight:700;cursor:pointer;position:relative;overflow:hidden;width:100%}
.msg-send:hover,.BlogSearch .search-action:hover{-webkit-box-shadow:0 3px 5px 0 rgba(0,0,0,0.4);box-shadow:0 3px 5px 0 rgba(0,0,0,0.4)}
.msg-send:before{content:"\f1d8";font-family:FontAwesome;position:absolute;top:10px;$startSide:10px;z-index:1;font-weight:normal}
.msg-send:hover input[type="submit"]{color:#2c2c2c}
.ltr .msg-send:before{-webkit-transform:rotateY(180deg);transform:rotateY(180deg)}
.msg-send:hover:before{-webkit-animation:subs .3s ease-in-out;animation:subs .3s ease-in-out}
@-webkit-keyframes subs{
from{top:37px;$startSide:38px}
to{top:10px;$startSide:10px}
}
@keyframes subs{
from{top:37px;$startSide:38px}
to{top:10px;$startSide:10px}
}

/* LinkList & PageList & TextList */
.LinkList .widget-content li a, *:not(header) .PageList .widget-content li a,.TextList .widget-content li{display:block;padding:13px 0;font-size:14px}
.LinkList .widget-content li:first-child a, *:not(header) .PageList .widget-content li:first-child a,.TextList .widget-content li:first-child{padding-top:0}
aside .LinkList .widget-content li a,aside .PageList .widget-content li a,aside .TextList .widget-content li{color:$(aside.link);border-bottom:1px solid $(aside.line)}
footer .LinkList .widget-content li a,footer .PageList .widget-content li a,footer .TextList .widget-content li{color:$(footer.link);display:block;border-bottom:1px solid $(footer.line)}
aside .LinkList .widget-content li a::before, footer .LinkList .widget-content li a::before{content:"\f08b"}
.TextList .widget-content li::before{content:"\f129";font-size:10px}
*:not(header) .PageList .widget-content li a::before{content:"\f0f6"}
aside .LinkList .widget-content li a::before, footer .LinkList .widget-content li a::before,*:not(header) .PageList .widget-content li a::before,.TextList .widget-content li::before{display:inline-block;vertical-align:top;font-family:fontawesome;margin-$endSide:10px;font-size:20px}
aside .LinkList .widget-content li a::before,aside .PageList .widget-content li a::before,aside .TextList .widget-content li::before{color:$(aside.text)}
footer .LinkList li a::before,footer .PageList .widget-content li a::before,footer .TextList .widget-content li::before{color:$(footer.text)}
aside .LinkList .widget-content li a:hover, footer .LinkList .widget-content li a:hover, *:not(header) .PageList .widget-content li a:hover{color:$(keycolor);border-bottom:1px solid $(keycolor)}
aside .LinkList .widget-content li a:hover::before, footer .LinkList .widget-content li a:hover::before{-webkit-animation:LinkIcon 0.2s linear;animation:LinkIcon 0.2s linear;color:$(keycolor)}
*:not(header) .PageList .widget-content li a:hover::before{color:$(keycolor)}
@-webkit-keyframes LinkIcon{100%{-webkit-transform:translateX(-5px);transform:translateX(-5px)}}
@keyframes LinkIcon{100%{-webkit-transform:translateX(-5px);transform:translateX(-5px)}}

/* Popular Posts */
.PopularPosts article{margin-bottom:15px;padding-bottom:15px;overflow:hidden}
.PopularPosts article:last-of-type{margin-bottom:0;border-bottom:none}
aside .PopularPosts article{border-bottom:1px solid $(aside.line)}
footer .PopularPosts article{border-bottom:1px solid $(footer.line)}
.PopularPosts .post-date{display:inline-block;background-color:$(aside.line);color:$(home.cate.text);text-align:$startSide;font-size:10px;border-radius:5px;padding-$startSide:5px;padding-$endSide:10px;margin-bottom:5px}
.PopularPosts .post-date i{background-color:rgba(0,0,0,0.2);font-size:12px;display:inline-block;vertical-align:middle;padding:5px;margin-$endSide:5px}
.PopularPosts .item-thumbnail{display:block;overflow:hidden;float:$startSide;width:72px;height:72px;margin-$endSide:15px;border-radius:5px}
.PopularPosts .item-thumbnail img{height:100%;display:block}
.PopularPosts .item-thumbnail:hover img{-webkit-transform:scale(1.1) rotate(3deg);transform:scale(1.1) rotate(3deg)}
.PopularPosts .post-title{overflow:hidden;margin:0 0 5px;font-weight:700;font-size:16px}
aside .PopularPosts .post-title a{color:$(aside.link)}
footer .PopularPosts .post-title a{color:$(footer.link)}
.PopularPosts .post-title a:hover{color:$(keycolor)}
.snippet-item{font-size:12px;text-align:justify;line-height:1.5em;margin:0}
aside .snippet-item{color:$(aside.text)}
footer .snippet-item{color:$(footer.text)}

/* Archive Widget */
.BlogArchive select{background:transparent;width:100%;padding:5px 20px;margin:0 auto;display:block;font-family:inherit;font-size:12px}
.BlogArchive select:focus{border:1px solid $(keycolor)}
aside .BlogArchive select{border:1px solid $(aside.line);color:$(aside.link)}
footer .BlogArchive select{border:1px solid $(footer.line);color:$(footer.link)}

/* Flat */
.flat .archivedate .post-count{font-style:normal;float:$endSide}
aside .flat .archivedate i{color:$(aside.text)}
footer .flat .archivedate i{color:$(footer.text)}
.BlogArchive .flat .archivedate a{display:block;padding:7px 2px}
aside .BlogArchive .flat .archivedate a{color:$(aside.link);border-bottom:1px dotted $(aside.line)}
footer .BlogArchive .flat .archivedate a{color:$(footer.link);border-bottom:1px dotted $(footer.line)}
aside .BlogArchive .flat .archivedate a::before{color:$(aside.line)}
footer .BlogArchive .flat .archivedate a::before{color:$(footer.text)}
.BlogArchive .flat .archivedate a::before{display:inline-block;content:"\f08d";-webkit-transform:rotate(to $endSide);transform:rotate(to $endSide);font-family:fontawesome;margin-$endSide:10px;vertical-align:middle}
.BlogArchive .flat .archivedate:hover a{color:$(keycolor);border-bottom:1px dotted $(step.color)}
.BlogArchive .archivedate:hover a:before{color:$(keycolor)}

/*  Hierarchy */
.hierarchy .hierarchy{margin-$startSide:10px}
aside .hierarchy-title{background:$(aside.line);margin-bottom:5px;padding:8px 15px}
footer .hierarchy-title{background:$(footer.line);margin-bottom:5px;padding:5px 20px}
aside .hierarchy .post-count-link,aside .hierarchy ul.posts a{color:$(aside.link)}
footer .post-count-link, footer .hierarchy ul.posts a{color:$(footer.link)}
.hierarchy .post-count{float:$endSide;color:#999}
aside .hierarchy .post-count{color:$(aside.text)}
footer .hierarchy .post-count{color:$(footer.text)}
.hierarchy ul.posts{margin-$startSide:0}
.hierarchy ul.posts a{font-size:12px;display:block;padding:5px 0}
.hierarchy ul.posts a:hover{border-bottom:1px solid $(step.color);color:$(keycolor);padding-$startSide:5px}
aside .hierarchy ul.posts a{border-bottom:1px solid $(aside.line)}
footer .hierarchy ul.posts a{border-bottom:1px solid $(footer.line)}

/* Label Widget */
.cloud-label-widget-content{overflow:hidden}
.cloud-label-widget-content .label-name{float:$startSide;background:-webkit-gradient(linear,$endSide,from($(keycolor)),to($(step.color)));background:linear-gradient(to $endSide,$(keycolor),$(step.color));padding:7px 15px;margin-$endSide:7px;margin-bottom:7px;border-radius:5px;font-size:14px;color:$(grad.color);font-weight:700}
.cloud-label-widget-content .label-name::before{content:"\f02b";font-family:fontawesome;display:inline-block;vertical-align:top;margin-$endSide:5px;border-radius:100%;margin-top:-4px;font-size:14px;padding-top:5px;text-align:center;font-weight:400}
.cloud-label-widget-content .label-name:hover{-webkit-box-shadow:0 3px 5px 0 rgba(0,0,0,0.4);box-shadow:0 3px 5px 0 rgba(0,0,0,0.4)}
.cloud-label-widget-content .label-name:hover:before{-webkit-animation:label .3s ease-in-out;animation:label .3s ease-in-out}
.list-label-widget-content a{display:block;padding:7px 0}
@-webkit-keyframes label{
50%{-webkit-transform:rotate(60deg);transform:rotate(60deg)}
100%{-webkit-transform:rotate(-60deg);transform:rotate(-60deg)}
}
@keyframes label{
50%{-webkit-transform:rotate(60deg);transform:rotate(60deg)}
100%{-webkit-transform:rotate(-60deg);transform:rotate(-60deg)}
}
aside .list-label-widget-content .label-name{color:$(aside.link);border-bottom:1px dotted $(aside.line)}
footer .list-label-widget-content .label-name{color:$(footer.link);border-bottom:1px dotted $(footer.line)}
.list-label-widget-content .label-name::before{content:"\f07b";font-family:fontawesome;margin-$endSide:10px;display:inline-block;vertical-align:top;-webkit-transition:.3s ease-out;transition:.3s ease-out;width:20px;text-align:$endSide}
aside .list-label-widget-content .label-name::before{color:$(aside.text)}
footer .list-label-widget-content .label-name::before{color:$(footer.text)}
.list-label-widget-content .label-name:hover::before{content:"\f07c";color:$(keycolor)!important}
.list-label-widget-content .label-count{margin-top:-28px}
aside .list-label-widget-content .label-count{float:$endSide;color:$(aside.text)}
footer .list-label-widget-content .label-count{float:$endSide;color:$(footer.text)}
aside .list-label-widget-content .label-name:hover{color:$(keycolor);border-bottom:1px dotted $(keycolor)}
footer .list-label-widget-content .label-name:hover{color:$(keycolor);border-bottom:1px dotted $(keycolor)}

/* Statistics */
.Stats img{width:auto;height:auto;display:inline-block;vertical-align:-4px;border-radius:0;margin-$endSide:5px}
.Stats .widget-content{text-align:center;font-size:30px;font-weight:700;font-family:Arial}
aside .text-counter-wrapper{color:$(aside.link);margin:0 5px;vertical-align:5px}
footer .text-counter-wrapper{color:$(footer.link);margin:0 5px;vertical-align:5px}

/* Contact Form */
.post-body #ContactForm93{padding:$(main.padding);border:1px solid $(home.cate.line);border-radius:5px}
.post-body #ContactForm93 .headline{display:none}
.ContactForm form{position:relative}
.ContactForm input[type='text'],.ContactForm textarea{display:block;width:100%;margin-bottom:5px;padding-top:15px;padding-bottom:5px;padding-$startSide:30px;padding-$endSide:20px;border:0;resize:vertical;outline:0;font-family:inherit;font-size:14px;font-weight:700;line-height:2em;background-color:transparent;position:relative;z-index:2}
aside .ContactForm input[type='text'],aside .ContactForm textarea{border-bottom:2px solid $(aside.line);color:$(aside.text)}
footer .ContactForm input[type='text'], footer .ContactForm textarea{border-bottom:2px solid $(footer.line);color:$(footer.text)}
.ContactForm textarea{min-height:150px}
.ContactForm input[type='text']:focus,.ContactForm textarea:focus{border-bottom:2px solid $(keycolor)}
.ContactForm i{position:absolute;$startSide:0;font-size:18px}
aside .ContactForm i{color:$(aside.text)}
footer .ContactForm i{color:$(footer.text)}
.ContactForm input[type='text']:foucs + i,.ContactForm textarea:focus + i{color:$(keycolor)}
.ContactForm i:nth-of-type(1){top:17px}
.ContactForm i:nth-of-type(2){top:70px}
.ContactForm i:nth-of-type(3){top:130px}
.ContactForm label{position:absolute;$startSide:30px;font-size:14px;font-weight:bold}
aside .ContactForm label{position:absolute;$startSide:30px;color:$(aside.text)}
footer .ContactForm label{position:absolute;$startSide:30px;color:$(footer.text)}
.ContactForm label:nth-of-type(1){font-size:14px;top:10px}
.ContactForm label:nth-of-type(2){font-size:14px;top:60px}
.ContactForm label:nth-of-type(3){font-size:14px;top:120px}
.ContactForm input[type='text']:nth-of-type(1):valid ~ label:nth-of-type(1),.ContactForm input[type='text']:nth-of-type(1):focus ~ label:nth-of-type(1){font-size:10px;top:-5px}
.ContactForm input[type='text']:nth-of-type(2):valid ~ label:nth-of-type(2),.ContactForm input[type='text']:nth-of-type(2):focus ~ label:nth-of-type(2){font-size:10px;top:50px}
.ContactForm textarea:valid ~ label:nth-of-type(3),.ContactForm textarea:focus ~ label:nth-of-type(3){font-size:10px;top:105px}
.ContactForm input[type='text']:valid + i + label,.ContactForm textarea:valid + i + label{color:$(keycolor)!important}
.ContactForm input[type='button']{outline:0;border:0;border-radius:5px;background-color:$(keycolor);font-family:inherit;font-weight:700;font-size:18px;padding:10px 20px;float:$endSide;cursor:pointer;color:$(grad.color)}
.ContactForm input[type='button']:hover{padding:10px 30px}
.ContactForm input[type='text']:valid+i,.ContactForm textarea:valid+i{color:$(keycolor)!important}
aside .contact-state{float:$startSide;color:$(aside.text)}
footer .contact-state{float:$startSide;color:$(footer.text)}
.contact-state img{float:$startSide;line-height:90px;margin-$endSide:10px}
.contact-state p{line-height:11px}
body .ContactForm input[type='text']:valid,body .ContactForm textarea:valid{border-bottom:2px solid $(keycolor)!important}

/* Feed */
.Feed li{list-style:square;margin-$startSide:20px;padding-bottom:5px;margin-top:5px;border-bottom:1px solid}
.Feed li:hover{color:$(keycolor);border-color:$(keycolor)}
aside .Feed li{color:$(aside.text);border-color:$(aside.line)}
footer .Feed li{color:$(footer.text);border-color:$(footer.line)}
.Feed .item-title a{font-weight:700;display:block}
aside .Feed .item-title a{color:$(aside.link)}
footer .Feed .item-title a{color:$(footer.link)}
.Feed .item-title a:hover{color:$(keycolor)}
aside .Feed .item-date{color:$(aside.text)}
footer .Feed .item-date{color:$(aside.text)}
aside .Feed .item-author{color:$(aside.text)}
footer .Feed .item-author{color:$(footer.text)}

/* Profile */
.Profile .profile-img{float:$startSide;width:72px;height:72px;margin-$endSide:15px}
.Profile a.profile-link.g-profile{display:block;font-weight:700;font-size:16px;margin-bottom:5px}
aside .Profile a.profile-link.g-profile{color:$(aside.link)}
footer .Profile a.profile-link.g-profile{color:$(footer.link)}
.Profile .widget-content.individual a.profile-link:not([data-onload]){font-size:12px;width:100%;margin:10px auto 0;display:block;text-align:center;border-radius:2px;padding:5px 10px;line-height:20px;background:-webkit-gradient(linear,$endSide,from($(keycolor)),to($(step.color)));background:linear-gradient(to $endSide,$(keycolor),$(step.color));color:$(grad.color)}
.Profile .widget-content.individual dl.profile-datablock{margin-bottom:0}
.Profile .team-member .profile-img{width:50px;height:50px}
.Profile .widget-content.team li{clear:both;margin-bottom:5px;display:block;overflow:hidden;padding-bottom:5px;border-bottom:1px solid}
aside .Profile .widget-content.team li{border-color:$(aside.line)}
footer .Profile .widget-content.team li{border-color:$(footer.line)}
.Profile .widget-content.team li:last-of-type{margin-bottom:0;padding-bottom:0;border-bottom:none}
aside .profile-textblock, aside .profile-data{color:$(aside.text)}
footer .profile-textblock, footer .profile-data{color:$(footer.text)}

/* [GAL] Widget */
.gallery-widget a{float:$startSide;border-radius:5px;overflow:hidden;opacity:.9}
.gallery-widget.gal-1 a{width:100%;margin-bottom:15px}
.gallery-widget.gal-2 a{width:47.5%;margin-$endSide:5%;margin-bottom:15px}
.gallery-widget.gal-3 a{width:30%;margin-$endSide:5%;margin-bottom:15px}
.gallery-widget.gal-4 a{width:22%;margin-$endSide:4%;margin-bottom:12px}
.gallery-widget.gal-2 a:nth-of-type(2n),.gallery-widget.gal-3 a:nth-of-type(3n),.gallery-widget.gal-4 a:nth-of-type(4n){margin-$endSide:0}
.gallery-widget{overflow:hidden}
.gallery-widget a:hover{opacity:1}
.gallery-widget a img{display:block;width:100%;height:100%}

/* [SOC] Widget */
.social-widget{overflow:hidden;padding:$(main.padding);border:1px solid;list-style:none;border-radius:5px;display:flex;flex-wrap:wrap;justify-content:center;}
aside .social-widget{border-color:$(aside.line)}
footer .social-widget{border-color:$(footer.line)}
.social-widget ul{display:flex;flex-wrap:wrap;justify-content:center}
.social-widget li{width:24%;margin:0 0.5%;max-width:70px}
.social-widget li i{text-align:center;display:block;width:80%;height:55px;line-height:55px;margin:auto;color:#FFF;font-size:24px;border-radius:100%;-webkit-transform:translate(0,9px);-ms-transform:translate(0,9px);transform:translate(0,9px)}
.social-widget li svg{width:56px;height:56px;padding:16px;fill:#FFF}
.social-widget li:hover i,.social-widget li:hover svg{-webkit-transform:translate(0);-ms-transform:translate(0)}
.social-widget li div{padding:5px 7px;border-radius:10px;font-size:11px;text-align:center;position:relative}
.social-widget li:nth-of-type(n+5) div{margin-bottom:0}
aside .social-widget li div{background-color:$(aside.line);color:$(aside.link)}
footer .social-widget li div{background-color:$(footer.line);color:$(footer.link)}
.social-widget li div:after{content:"";display:block;border-width:5px;border-style:solid;position:absolute;top:-10px;$endSide:44%}
aside .social-widget li div:after{border-color:transparent transparent $(aside.line)}
footer .social-widget li div:after{border-color:transparent transparent $(footer.line)}

/* [ACC] Widget */
.acc-head{background:-webkit-gradient(linear,$endSide,from($(keycolor)),to($(step.color)));background:linear-gradient(to $endSide,$(keycolor),$(step.color));padding:10px 15px;margin-bottom:3px;color:$(grad.color);font-weight:bold;font-size:14px;cursor:pointer;border-radius:5px;padding-$endSide:40px;position:relative}
.acc-head:after{content:"\f078";font-family:fontawesome;font-weight:normal;position:absolute;$endSide:20px;top:10px;font-size:12px}
.acc-head.open:after{content:"\f077"}
.acc-body{padding:$(main.padding);margin-bottom:5px;text-align:justify;border-radius:5px;overflow:hidden;display:none}
aside .acc-body{background-color:$(aside.line);color:$(aside.text)}
footer .acc-body{background-color:$(footer.line);color:$(footer.text)}
.accordion-widget > .acc-body:nth-of-type(2){display:block}

/* Recent Comments Widget */
.recent-comments{overflow:hidden}
.recent-comments .comment{margin-bottom:15px;padding-bottom:15px;overflow:hidden}
aside .recent-comments .comment{border-bottom:1px solid $(aside.line)}
footer .recent-comments .comment{border-bottom:1px solid $(footer.line)}
.recent-comments .comment:last-child{margin-bottom:0;border-bottom:0}
.comments-img-wrap{float:$startSide;width:50px;height:50px;border-radius:100px;margin-$endSide:15px;overflow:hidden}
aside .comments-img-wrap{border:3px solid $(aside.line)}
footer .comments-img-wrap{border:3px solid $(footer.line)}
.recent-comments .comment .comm{float:$startSide;width:calc(100% - 65px)}
.recent-comments .comment .comm-author{text-overflow:ellipsis;white-space:nowrap;font-size:12px;font-weight:700;float:$startSide;height:21px;margin-$endSide:10px;overflow:hidden}
aside .recent-comments .comment .comm-author{color:$(aside.text)}
footer .recent-comments .comment .comm-author{color:$(footer.text)}
.recent-comments .comment .comm-author:hover{text-decoration:underline}
.recent-comments .comment .details{float:$endSide;overflow:hidden}
.recent-comments .comment .details span{margin-$endSide:0;font-size:9.5px}
.recent-comments .comment p{text-align:$startSide;width:100%;margin:0 0 2px;font-size:10px;line-height:1.8em;overflow:hidden;font-weight:700}
aside .recent-comments .comment p{color:$(aside.link)}
footer .recent-comments .comment p{color:$(footer.link)}
aside .recent-comments .comment .leave-comm{color:$(aside.link)}
footer .recent-comments .comment .leave-comm{color:$(footer.text)}
.recent-comments .comment .leave-comm{display:block;padding-$startSide:15px;overflow:hidden;font-size:10px;position:relative}
.recent-comments .comment .leave-comm:before{color:$(step.color);content:"\f086";font-family:FontAwesome;position:absolute;top:0;$startSide:0}
.recent-comments .comment .leave-comm:hover{text-decoration:underline;color:$(step.color)}
.attachment:before{font-family:fontawesome;font-weight:400;font-size:14px;vertical-align:top;display:inline-block}
.attachment.att-pic:before{content:"\f03e"}
.attachment.att-vid:before{content:"\f16a"}
.attachment{margin:0 5px;color:$(keycolor);white-space:nowrap}

/* Costom Posts Widget [ Slider ] */
.fadeOut{-webkit-animation-name:fadeOut;animation-name:fadeOut}
.CusWidget .author-prof{top:15px}
.CusWidget .post-date{top:40px}
.CusWidget .caption h3{font-size:18px;margin:0;clear:both;line-height:1.5em}
.CusWidget .slider-carousel .item{width:100%}
.CusWidget .slider-carousel .img-wrap{width:100%;height:230px}
.vCar-screen{overflow:hidden}
.vCar-wrapper{position:relative;top:0}
.CusWidget .v-carousel{position:relative;border-radius:5px}
.CusWidget .v-carousel .item{overflow:hidden;position:relative;border-radius:5px}
.CusWidget .v-carousel .img-wrap{width:100%;height:200px}
aside .CusWidget .v-carousel{border:1px solid $(aside.line);padding:$(main.padding)}
.CusWidget button{position:absolute;top:-55px;border-radius:5px;width:30px;height:30px;padding:0;text-align:center;border:none;cursor:pointer;transition:0s;font-size:10px}
aside .CusWidget button{background-color:$(aside.line);color:$(aside.text)}
footer .CusWidget button{background-color:$(footer.line);color:$(footer.text)}
.CusWidget button:hover{color:$(grad.color);background:-webkit-gradient(linear,left,from($(keycolor)),to($(step.color)));background:linear-gradient(to left,$(keycolor),$(step.color))}
.CusWidget button.splide__arrow--next{$endSide:0}
.CusWidget button.splide__arrow--prev{$endSide:35px}
.CusWidget button svg{width:15px;position:static;float:none;display:block;margin:0 auto;transition:0s;pointer-events:none}


/* Featured Post */
.FeaturedPost h3{margin:0 0 5px 0;line-height:1.5em;font-size:18px}
.FeaturedPost .item-thumbnail,.FeaturedPost .item-thumbnail img{display:block;text-align:center;margin:0 auto;width:100%;height:auto}
.FeaturedPost .item-thumbnail{min-height:150px;overflow:hidden;margin-bottom:5px;border-radius:5px;}
.FeaturedPost .item-thumbnail.NoImage{min-height:180px}
aside .FeaturedPost h3{color:$(aside.link)}
footer .FeaturedPost h3{color:$(footer.link)}
.widget.FeaturedPost h3:hover{color:$(keycolor)}
aside .FeaturedPost p{color:$(aside.text)}
footer .FeaturedPost p{color:$(footer.text)}

/* Costom Posts Widget [ Thumbs ] */
aside .rand-content{padding:$(main.padding);border:1px solid $(aside.line);border-radius:5px}
.rand-content div{padding-bottom:10px;margin-bottom:10px;overflow:hidden}
aside .rand-content > div{border-bottom:1px solid $(aside.line)}
footer .rand-content > div{border-bottom:1px solid $(footer.line)}
.rand-content .img-wrap{float:$startSide;height:72px;width:90px;margin-$endSide:15px}
aside .rand-content div:last-child{padding-bottom:0;margin-bottom:0;border-bottom:none}
.rand-content h3{margin:0;font-size:16px;line-height:1.5em}
aside .rand-content h3 a{color:$(aside.link)}
footer .rand-content h3 a{color:$(footer.link)}
.rand-content h3 a:hover{color:$(keycolor)}
.rand-content .details > *{line-height:1.5em;font-size:10px;display:inline-block;vertical-align:middle;padding:8px 0;}

/* Pages (Common)
===================*/
/* Pagination */
#Pagination{margin-top:15px;text-align:center;clear:both;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}
#Pagination span{color:$(home.cate.text);width:35px;height:35px;line-height:35px;background-color:$(home.cate.line);border-radius:100%;display:inline-block;text-align:center;margin:0 4px;cursor:pointer}
#Pagination span.hid-num{display:none}
#Pagination span,#Pagination a{-webkit-transition:none;transition:none}
#Pagination button:hover,#Pagination span:hover,#Pagination .curr{color:$(grad.color)!important;background:-webkit-gradient(linear,$endSide,from($(keycolor)),to($(step.color)));background:linear-gradient(to $endSide,$(keycolor),$(step.color));color:$(grad.color)}
.nums{width:351px;overflow:hidden;max-width:calc(100% - 90px);margin:auto;display:inline-block;height:35px}
#Pagination button{border:none;height:35px;width:35px;background-color:$(home.cate.line);color:$(home.cate.text);display:inline-block;vertical-align:top;line-height:35px;font-size:20px;cursor:pointer;margin:0 2px;border-radius:100%}
#Pagination > button{font-family:fontawesome}
.rtl .pg-prev:before, .ltr .pg-next:before{content:"\f101"}
.rtl .pg-next:before, .ltr .pg-prev:before{content:"\f100"}
.static-page .entry-title{line-height:1.35;}
.old-pagination{display:flex;align-items:center;justify-content:space-between}
.old-pagination a{background-color:$(home.cate.line);color:$(home.cate.text);padding:0 20px;height:40px;display:flex;align-items:center;justify-content:center;border-radius:5px;font-weight:700}
.old-pagination a:hover{background:linear-gradient(to $endSide,$(keycolor),$(step.color));color:$(grad.color)}
a.blog-pager-older-link{margin-$startSide:auto}
a.blog-pager-newer-link{margin-$endSide:auto}

/* Homepage
===================*/
/* Category Error */
.temp-error{text-align:center}
.temp-error b{background-color:#d00;display:inline-block;color:#FFF;margin:0 auto 10px;padding:3px 10px;border-radius:5px;text-align:center}
.temp-error span{display:block;clear:both;color:$(home.cate.text);text-align:center;line-height:2em}
.temp-error i{font-weight:700;font-style:inherit;background-color:#eee;padding:0 10px;border-radius:100px}

/* Common */
.cate .headline{display:none}
.home-cate{margin-bottom:$(main.margin);clear:both}
.home-cate .widget-content{padding:$(main.padding);border:1px solid $(home.cate.line);overflow:hidden;border-radius:5px}
.cate-link{margin:0;font-size:16px;overflow:hidden;max-height:45px}
.cate-link a{color:$(home.cate.link);line-height:1.3em}
.cate-link a:hover{color:$(keycolor)}
.cate-snippet{font-size:14px;color:$(home.cate.text);line-height:1.5em;margin:5px 0 10px}
.home-cate .details{margin-top:5px;line-height:1em}
.cate-carousel .Item{padding:0 10px;width:25%}
.home-cate .Item{overflow:hidden}
.details > *{display:inline-block;vertical-align:top;font-size:11px}
.details > * i{color:$(keycolor);margin-$endSide:5px}
.details > *:first-child{margin-$endSide:10px}
.details a:hover{text-decoration:underline;color:$(keycolor)}
.details > *{color:$(home.cate.text)}
aside .details > *{color:$(aside.text)}
footer .details > *{color:$(footer.text);vertical-align:middle;}
.img-wrap:before{content:"\f0f6";font-size:20px;position:absolute;top:50%;$startSide:50%;margin-$startSide:-25px;margin-top:-25px;font-family:fontawesome;color:$(grad.color);line-height:51px;width:50px;height:50px;text-align:center;z-index:1;-webkit-transform:scale(0);transform:scale(0);opacity:0}
.img-wrap:after{content:"";border-radius:100px;font-size:20px;border:4px solid $(grad.color);position:absolute;top:50%;$startSide:50%;margin-$startSide:-25px;margin-top:-25px;width:50px;height:50px;-webkit-transform:scale(1.5);transform:scale(1.5);opacity:0}
.img-wrap:hover:before,.img-wrap:hover:after{-webkit-transform:scale(1);transform:scale(1);opacity:1}

/* Section Type :Sided */
.two-cols .section{width:calc((100% - 15px)/3);float:$startSide}
.two-cols .section.wide-right{width:calc((100% - 15px) / 3 * 2);margin-$endSide:15px}
.two-cols .section.wide-left{width:calc(((100% - 15px) / 3) * 2);margin-$startSide:15px}
.two-cols.no-wide .section{width:calc((100% - 15px) / 2)}
.two-cols.no-wide .section:first-child{margin-$endSide:15px}
.three-cols .section{width:calc((100% - 30px)/3);float:$startSide}
.three-cols .section:nth-of-type(2){margin-$endSide:15px;margin-$startSide:15px}
.cate-sided .Item:first-of-type{padding-bottom:15px;margin-bottom:15px;border-bottom:1px solid $(home.cate.line)}
.cate-sided .Item:first-of-type .img-wrap{width:100%;height:200px}
.cate-sided .Item:first-of-type .cate-link{margin-top:10px}
.cate-sided .Item:first-of-type .cate-snippet{margin:10px 0}
.cate-sided .Item:nth-of-type(n+2){max-height:86px;padding-bottom:15px;margin-bottom:15px;border-bottom:1px solid $(home.cate.line)}
.cate-sided .Item:nth-of-type(n+2) .img-wrap{float:$startSide;width:90px;height:70px;margin-$endSide:15px}
.cate-sided .Item:last-of-type{margin-bottom:0;border-bottom:none;padding-bottom:0}
.cate-sided .Item:nth-of-type(n+2) .img-wrap:after,.cate-sided .Item:nth-of-type(n+2) .img-wrap:before{display:none}

/* Section Type :Cover */
.cate-cover .free-width .Item:first-of-type .img-wrap{float:$startSide;width:300px;height:180px;margin-$endSide:15px}
.cate-cover .tight-width .Item:first-of-type .img-wrap{display:block;height:200px;margin-bottom:15px}
.cate-cover .Item:first-of-type .cate-snippet{margin:10px 0}
.cate-cover .free-width .Item:nth-of-type(n+2){float:$startSide;width:49%}
.cate-cover .free-width .Item:nth-of-type(n+2):nth-of-type(even){margin-$endSide:2%}
.cate-cover .Item:nth-of-type(n+2){margin-top:15px;padding-top:15px;border-top:1px solid $(home.cate.line)}
.cate-cover .Item:nth-of-type(n+2) .img-wrap{float:$startSide;width:90px;height:70px;margin-$endSide:15px}
.cate-cover .Item:nth-of-type(n+2) .img-wrap:after, .cate-cover .Item:nth-of-type(n+2) .img-wrap:before{display:none}

/* Section Type :Video */
.cate-video .Item{float:$startSide}
.cate-video .cate-link{max-height:63px}
.cate-video .Item{margin-top:15px;padding-top:15px;border-top:1px solid $(home.cate.line)}
.cate-video .Item:nth-of-type(-n+3){margin-top:0;padding-top:0;border-top:none}
.cate-video .Item:nth-of-type(3n-1){margin-$startSide:1%;margin-$endSide:1%}
.cate-video .free-width .Item{width:32.6666%}
.cate-video .tight-width .Item{width:100%;margin:0;margin-bottom:15px}
.cate-video .img-wrap{float:$startSide;width:170px;height:120px;margin-$endSide:15px;position:relative}
.cate-video canvas{position:absolute;top:50%;z-index:5;margin-top:-25px;$startSide:50%;margin-$startSide:-25px;opacity:0}
.cate-video .img-wrap i{content:"\f04b";font-family:fontawesome;position:absolute;top:50%;right:50%;color:#FFF;font-size:24px;margin-top:-9px;margin-right:-12px;-webkit-transform:scale(0);transform:scale(0);text-shadow:0 0 3px rgba(0,0,0,0.5);-webkit-transition:.3s ease-in-out;transition:.3s ease-in-out;line-height:18px}
.cate-video .img-wrap:hover i{-webkit-transform:scale(1);transform:scale(1)}
.cate-video .img-wrap:hover canvas{opacity:1}
.cate-video .img-wrap:after,.cate-video .img-wrap:before{display:none}

/* Section Type :Slideshow */
.slideshow-thumbnail{position:relative}
.free-width .slideshow-thumbnail{float:$startSide;width:65.188%;height:350px;margin-$endSide:1.5%}
.tight-width .slideshow-thumbnail{width:100%;height:200px;margin-bottom:15px}
.slideshow-thumbnail .splide__list, .slideshow-thumbnail .splide__track,.slideshow-thumbnail .img-wrap{width:100%!important;height:100%!important}
.cate-slideshow .Item{height:62px;margin-bottom:10px;padding:10px;background-color:$(home.cate.line);border-radius:5px;cursor:pointer;position:relative;color:$(home.cate.text)}
.cate-slideshow .Item:last-of-type{margin-bottom:0}
.cate-slideshow .free-width .slideshow-thumbs{float:left;width:33.31%}
.cate-slideshow .tight-width .Item{height:auto}
.cate-slideshow .Item h3{pointer-events:none;color:inherit}
.cate-slideshow .Item a{font-size:14px;font-weight:700;color:inherit;transition:none}
.cate-slideshow .Item.is-active{background:-webkit-gradient(linear,$endSide,from($(keycolor)),to($(step.color)));background:linear-gradient(to $endSide,$(keycolor),$(step.color))}
.cate-slideshow .Item.is-active a{color:$(grad.color)!important}
.cate-slideshow .Item.is-active:after{content:"";display:block;border-width:8px;border-style:solid;border-color:transparent transparent transparent $(step.color);position:absolute;top:9px;$startSide:-15px}
.s-progress{background:-webkit-gradient(linear,left,from($(keycolor)),to($(step.color)));background:linear-gradient(to left,$(keycolor),$(step.color));width:0;height:3px;position:absolute;top:0;right:0;z-index:5;width:0%}

/* Section Type :Carousel */
.cate-carousel .widget-content{overflow:visible;padding:$(main.padding) -webkit-calc($(main.padding)/2);padding:$(main.padding) -moz-calc($(main.padding)/2);padding:$(main.padding) calc($(main.padding)/2)}
.cate-carousel .img-wrap{width:100%;height:200px}
.cate-carousel .cate-link{font-size:14px;max-height:42px;margin:10px 0 0;overflow:hidden}
.cate-carousel .label-name{position:absolute;bottom:20px;$startSide:20px}
.cate-carousel .splide__pagination{text-align:center;clear:both;margin-top:10px}
.cate-carousel .splide__pagination li{display:inline-block;vertical-align:middle;margin:0 2px}
.cate-carousel .splide__pagination__page{width:10px;height:10px;background-color:$(home.cate.line);border-radius:10px;border:none;padding:0;cursor:pointer}
.cate-carousel .splide__pagination__page.is-active{background-color:$(keycolor)!important;width:15px}
.cate-carousel .splide__arrows{position:absolute;top:-51px;$endSide:100px;position:absolute}
.cate-carousel .splide__arrows button{position:relative;background-color:$(home.cate.line);border-radius:5px;width:28px;height:28px;padding:0;text-align:center;border:none;cursor:pointer;transition:0s;color:$(home.cate.text);font-size:10px}
.cate-carousel .splide__arrows button:hover{color:$(grad.color)!important;background:-webkit-gradient(linear,left,from($(keycolor)),to($(step.color)));background:linear-gradient(to left,$(keycolor),$(step.color))}
.cate-carousel .splide__arrows button.splide__arrow--next{$endSide:0}
.cate-carousel .splide__arrows button.splide__arrow--prev{$endSide:5px}
.cate-carousel .splide__arrows button svg{fill:currentColor;width:15px;position:static;float:none;display:block;margin:0 auto;transition:0s;pointer-events:none}

/* Recent Posts Widget */
.index-posts .status a{float:$endSide;margin:0}
.index-posts .img-wrap{float:$startSide;width:300px;height:180px;margin-$endSide:15px}
.index-posts h3{margin:0 0 10px 0}
.index-posts .post-outer{padding-bottom:20px;border-bottom:1px solid $(home.cate.line);margin-bottom:20px;overflow:hidden}
.status-msg-body{border:1px solid $(home.cate.line);margin-bottom:20px;padding:$(main.padding);color:$(home.cate.text);border-radius:5px}
.status-msg-body a{color:$(keycolor);margin-$endSide:10px}
.status-msg-body b{color:$(home.cate.text)}
.status-msg-body a:hover{text-decoration:underline}
.v-index .index-posts{display:flex;flex-wrap:wrap;margin:0 -10px;overflow:hidden}
.v-index .index-posts .post-outer{width:calc(100% / 3);float:right;padding:0 10px;border-bottom:none}
.v-index .index-posts .img-wrap{width:100%;float:none;height:180px}
.v-index .index-posts .post-title{margin:10px 0;overflow:hidden;height:45px}

/* Index Ad */
#HTML505{margin-bottom:20px} 
.Blog #HTML505{border-bottom:1px solid $(home.cate.line);padding-bottom:20px}

/* Post Page
===================*/
.item-page header{margin-bottom:$(main.margin)}
.post-body,.post-body p{font-size:15px;color:$(post.text);line-height:2em}

/* Post Elements */
.post-body h3,.post-body h2,.post-body h4{background-color:$(post.headline.back);padding:10px 20px;color:$(post.headline);display:block;margin:5px 0 15px;border-bottom:3px solid #ddd;border-$startSide: 3px solid $(keycolor);box-shadow: 0 3px 6px rgba(0,0,0,0.05);border-radius:5px}
.post-body img{width:auto;height:auto;display:inline;max-width:100%;border-radius:5px}
.post-body img[data-src]{height:60vh;display:inline-block;width:100%;background-color:#000000;opacity:0.05}
.separator a{display:block}
.post-body br{content:'';margin:15px;display:block}
.post-body iframe{max-width:100%}
.post-body a{cursor:pointer}
.post-body a.d-link{text-decoration:none;color:#2465ef;cursor:pointer;font-weight:bold;}
.post-body a.d-link:hover{-webkit-box-shadow:0 -5px 0px inset #2465ef50;box-shadow:0 -5px 0px inset #2465ef50;color:$(post.text)!important}
.post-body *:not(.fa){font-family:'Cairo',sans-serif!important}
.post-body ol li{padding:8px 30px;margin-$startSide:15px;margin-bottom:15px;list-style:none;-webkit-box-shadow:0 2px 3px #ddd;box-shadow:0 2px 3px #ddd;font-size:17px;position:relative}
.post-body ol li:before{content:counter(li);counter-increment:ol li;background-color:$(keycolor);width:30px;height:30px;text-align:center;line-height:30px;margin-$endSide:15px;color:#FFF;border-radius:100%;font-size:17px;position:absolute;$startSide:-15px}
.post-body ol{counter-reset:li;padding-$startSide:0}
.post-body ol li:hover:after,.post-body ol li:hover:before{background-color:$(step.color)}
.post-body ol li:after{content:'';width:10px;height:100%;background-color:$(keycolor);position:absolute;top:0;$endSide:0}
.post-body ol li a{text-decoration:none}
.post-body ul{list-style-type:disc;padding-$startSide:40px}
.post-body li{color:$(post.text);padding-$startSide:10px}
.post-body ul li a{text-decoration:none}
.video-wrapper{width:100%;padding-top:56.25%;border-radius:5px;overflow:hidden;position:relative;}
.video-wrapper iframe{width:100%;height:100%;position:absolute;top:0;left:0;right:0;}

/* TOC */
#TOC{margin:0 5px 30px}
#TOC:empty{display:none}
#TOC > span{background-color:$(post.line);display:block;padding:10px 20px;font-weight:bold;font-size:16px;color:$(post.headline);cursor:pointer;user-select:none;border-radius:5px}
#TOC > span:after{content:"\f078";font-family:'FontAwesome';float:left;font-size:12px}
#TOC.open > span:after{content:"\f077"}
#TOC nav{display:none;padding:20px;border:1px solid $(post.line);border-radius:5px}
#TOC.open > nav{display:block}
#TOC li a{background-color:$(post.line);display:inline-block;margin-bottom:10px;padding-top:2px;padding-bottom:2px;padding-$startSide:0;padding-$endSide:20px;color:$(post.text);font-weight:700;
font-size:14px;border-radius:5px;}
#TOC li a:before{font-family:fontawesome;background-color:rgba(0,0,0,0.1);height:25px;width:25px;float:$startSide;margin:-2px 0;text-align:center;padding-top:3px;margin-$endSide:15px;color:$(post.text);font-size:12px;font-weight:400;-webkit-transition:font-size 0.1s,background-color .3s;transition:font-size 0.1s,background-color .3s}
.rtl #TOC li a:before{content:"\f060"}
.ltr #TOC li a:before{content:"\f061"}
#TOC li[data-tag="h3"]{margin-$startSide:30px}
#TOC li[data-tag="h4"]{margin-$startSide:60px}
#TOC li a:hover:before{background-color:$(keycolor);color:$(grad.color);font-size:14px}

/* See Also */
.see-also{border:1.5px solid $(post.line);padding:20px;border-radius:5px;margin:50px 0;text-align:$startSide;}
.see-also strong{background-color:$(keycolor);color:$(grad.color);padding:3px 20px;margin-top:-40px;float:$startSide;border-radius:100px;font-size:16px;line-height:2em}
.see-also ul{padding-$startSide:0;list-style:none}
.see-also li{border-bottom:0.5px solid $(post.line);padding:7px 0;display:flex;align-items:center}
.see-also li:last-of-type{border-bottom:none;padding-bottom:0}
.see-also li a{font-weight:700;color:$(post.link.color);font-size:14px}
.see-also li a:hover{color:$(keycolor)}
.see-also li:before{font-family:fontAwesome;margin-$endSide:15px;content:"\f06d";font-size:15px;color:$(keycolor)}

/* Ads */
.article-ad:empty{display:none}
.article-ad,.spc-ad{margin:20px auto;text-align:center}
.str-ad{margin-top:0}
.end-ad{margin-bottom:0}
.Middle-Ad.fixedAd{text-align:center;margin:15px}
.Middle-Ad:not(.fixedAd){clear:both;text-align:center}
.Middle-Ad:not(.fixedAd):not(:empty){margin:20px 0}

/* Full Width */
.fullwidth-topic{padding:$(main.padding);margin-top:35px;border:1px solid $(home.cate.line)}

/* Post Title */
.topic-title{margin:0;padding:15px;background:-webkit-gradient(linear,$endSide,from($(keycolor)),to($(step.color)));background:linear-gradient(to $endSide,$(keycolor),$(step.color));border-radius:5px;position:relative;-webkit-box-shadow:0 3px 5px 0 rgba(0,0,0,0.2);box-shadow:0 3px 5px 0 rgba(0,0,0,0.2);font-size:24px;color:#fff;text-align:center;line-height:1.3em;text-shadow:1px 1px 2px rgba(0,0,0,0.5)}

/* Post Tools */
article .topic-tools{display:inline-block;vertical-align:top;padding:5px 15px;background-color:$(post.nav.back);border-radius:0 0 5px 5px}

/* Zoom */
.zooming{text-align:center;width:110px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}
.zooming span{color:$(post.nav.color);margin:0 2px;display:inline-block;vertical-align:top;font-size:12px}
.zooming i{color:$(post.nav.color);border:2px solid $(keycolor);width:20px;display:inline-block;text-align:center;height:20px;border-radius:100px;vertical-align:top;padding-top:3px;font-size:11px;cursor:pointer}
.zooming i:hover{color:$(keycolor);border:2px solid $(keycolor)}
.zooming i.disb{color:$(post.nav.line);border:2px solid $(post.nav.line);cursor:not-allowed}

/* Post Detils */
.topic-details{margin:0 5%;font-size:0;width:calc(80% - 105px)}
.topic-details > *{margin-$endSide:10px;color:$(post.nav.color);display:inline-block;vertical-align:middle;margin-$endSide:15px;font-size:12px}
.topic-details i{margin-$endSide:5px;color:$(keycolor);width:12px;height:12px;display:inline-block}
.topic-details a:hover{color:$(keycolor);text-decoration:underline}
.topic-details .categ{position:relative;margin-$endSide:0}
.topic-details .categ a:first-of-type{position:relative}
.topic-details .categ a{display:inline-block;vertical-align:middle;margin-$endSide:5px}
.rtl .topic-details .categ a:first-of-type:after{content:"\f100"}
.ltr .topic-details .categ a:first-of-type:after{content:"\f101"}
.topic-details .categ a:first-of-type:after{line-height:12px;font-family:FontAwesome;font-size:12px;color:$(post.nav.line);display:inline-block;vertical-align:middle;margin-$startSide:5px}
.topic-details .categ a:last-of-type{margin-$endSide:0}
.topic{color:$(home.cate.text);padding:$(main.padding);margin:15px auto 0;overflow:hidden;text-align:justify;line-height:2.2;border:1px solid $(home.cate.line);margin-bottom:$(main.margin)}

/* Post Blockquote */
.post-body blockquote{width:90%;clear:both;-webkit-print-color-adjust:exact;padding:10px 30px;margin:15px auto;line-height:2;text-indent:15px;background:-webkit-gradient(linear,$endSide,from($(keycolor)),to($(step.color)));background:linear-gradient(to $endSide,$(keycolor),$(step.color));color:$(grad.color);border-radius:5px;position:relative}
.rtl .post-body blockquote:before,.ltr .post-body blockquote:after{content:"\f10e"}
.ltr .post-body blockquote:before,.rtl .post-body blockquote:after{content:"\f10d"}
.post-body blockquote:before{top:0;$startSide:-4px}
.post-body blockquote:before,blockquote:after{font-family:FontAwesome;font-size:20px;color:$(main.back);position:absolute}
.post-body blockquote:after{bottom:0;$endSide:15px}
.quote-share a{background:$(main.back);color:$(step.color);text-align:center;display:inline-block;width:35px;height:35px;text-indent:0;border-radius:100%;border:2px solid $(step.color);font-size:16px;margin:0 5px}
.quote-share a:hover{-webkit-transform:rotate(360deg);transform:rotate(360deg)}
.quote-share{position:absolute;$endSide:50px;bottom:-17px}

/* Post Pagination ----- */
.post-pages{position:relative;clear:both;overflow:hidden;margin-top:15px}
.post-pages:before{content:"";background-color:$(home.cate.line);height:5px;position:absolute;width:100%;top:15px}
a.next-page,a.prev-page{z-index:1;background-color:$(main.back);position:relative;border:2px solid $(keycolor);border-radius:100px;color:$(keycolor)}
a.next-page:hover,a.prev-page:hover{border:2px solid $(step.color);color:$(step.color)}
a.next-page:hover:before,a.prev-page:hover:before{background-color:$(step.color)}
.rtl a.prev-page{margin-right:10px;-webkit-box-shadow:10px 0 0, -20px 0 0 #FFF;box-shadow:10px 0 0, -20px 0 0 #FFF;float:$startSide;padding:5px 10px 5px 15px}
.ltr a.prev-page{margin-left:10px;-webkit-box-shadow:-10px 0 0, 20px 0 0 #FFF;box-shadow:-10px 0 0, 20px 0 0 #FFF;float:$startSide;padding:5px 10px 5px 15px}
.rtl a.next-page{margin-left:10px;-webkit-box-shadow:-10px 0 0, 20px 0 0 #FFF;box-shadow:-10px 0 0, 20px 0 0 #FFF;float:$endSide;padding:5px 15px 5px 10px}
.ltr a.next-page{margin-right:10px;-webkit-box-shadow:10px 0 0, -20px 0 0 #FFF;box-shadow:10px 0 0, -20px 0 0 #FFF;float:$endSide;padding:5px 15px 5px 10px}
a.next-page:before,a.prev-page:before{font-family:fontawesome;background-color:$(keycolor);width:21px;height:21px;border-radius:100px;text-align:center;color:$(grad.color);font-size:16px}
.rtl a.next-page:before{content:"\f104"}
.rtl a.prev-page:before{content:"\f105"}
.ltr a.next-page:before{content:"\f105"}
.ltr a.prev-page:before{content:"\f104"}
a.next-page:before{float:$endSide;margin-$startSide:10px}
a.prev-page:before{float:$startSide;margin-$endSide:10px}

/* Edit Post Button */
.edit-post a:before{content:"\f040";font-family:fontawesome;display:inline-block;font-weight:normal;margin-$endSide:10px}
.edit-post a{display:block;width:120px;text-align:center;padding:10px 0;border-radius:100px;font-weight:bold;background-color:$(step.color);margin:20px auto 0;color:$(main.back)}
.edit-post a:hover{width:130px;background-color:$(keycolor)}

/* Post Share */
.topic-share .social{display:block;width:100%;margin-$startSide:0;padding-top:$(main.margin);border-top:1px solid $(home.cate.line);text-align:center;position:static;top:auto;$startSide:auto;z-index:1;font-size:0;margin-top:$(main.margin)}
.topic-share .social li{display:inline-block;vertical-align:top;min-width:110px;margin:0 5px 5px 0;padding:0}
.topic-share .social li a:hover{-webkit-box-shadow:0 90px 75px 1px rgba(255,255,255,0.0) inset,0 3px 5px -2px rgba(0,0,0,0.3);box-shadow:0 90px 75px 1px rgba(255,255,255,0.0) inset,0 3px 5px -2px rgba(0,0,0,0.3);-webkit-animation:Share .2s ease-in-out;animation:Share .2s ease-in-out}
@-webkit-keyframes Share{
50%{-webkit-transform:scaleX(0.9);transform:scaleX(0.9)}
100%{-webkit-transform:scaleX(1.1);transform:scaleX(1.1)}
}
@keyframes Share{
50%{-webkit-transform:scaleX(0.9);transform:scaleX(0.9)}
100%{-webkit-transform:scaleX(1.1);transform:scaleX(1.1)}
}
.topic-share .social li a{display:block;padding:5px 6px;font-size:13px;font-family:inherit;color:#FFF;-webkit-box-shadow:0 90px 75px 1px rgba(255,255,255,0.1) inset;box-shadow:0 90px 75px 1px rgba(255,255,255,0.1) inset;border-radius:100px;text-align:$startSide;height:40px;overflow:hidden}
.topic-share .social li a:before{font-size:16px;font-family:fontawesome;display:inline-block;vertical-align:-2px;margin-$endSide:8px;background-color:rgba(0,0,0,0.2);width:30px;height:30px;text-align:center;border-radius:100px;padding-top:8px;-webkit-transition:.2s ease-in-out;transition:.2s ease-in-out;float:$startSide}
.topic-share .social li a:hover span:first-of-type{margin-top:2px}
.topic-share .social li a span:first-of-type{float:$startSide;margin-$endSide:5px;margin-top:8px;font-weight:bolder;-webkit-transition-delay:.2s;transition-delay:.2s}
.topic-share .social li a:hover span:last-of-type{margin-top:-11px}
.topic-share .social li a span:last-of-type{font-size:10px;color:rgba(255,255,255,0.5);display:block;clear:both;float:$startSide;margin-$startSide:38px;margin-top:6px;-webkit-transition-delay:.2s;transition-delay:.2s}
.topic-share .social li a:hover:before{background-color:#FFF;-webkit-animation:Share2 .2s ease-in-out;animation:Share2 .2s ease-in-out}
@-webkit-keyframes Share2{
50%{-webkit-transform:scale(0.8);transform:scale(0.8)}
100%{-webkit-transform:scale(1.3);transform:scale(1.3)}
}
@keyframes Share2{
50%{-webkit-transform:scale(0.8);transform:scale(0.8)}
100%{-webkit-transform:scale(1.3);transform:scale(1.3)}
}
.topic-share .social li a.fa-envelope:before{font-size:14px}
.topic-share .social li a.fa-facebook{background-color:#3b5998}
.topic-share .social li a.fa-twitter{background-color:#1da1f2}
.topic-share .social li a.fa-pinterest-p{background-color:#cc2127}
.topic-share .social li a.fa-google-plus{background-color:#dd4b39}
.topic-share .social li a.fa-phone{background-color:#0D660A}
.topic-share .social li a.fa-envelope{background-color:#7954ad}
.topic-share .social li a.fa-print{background-color:#555555}
.topic-share .social li a.fa-facebook:hover:before{color:#3b5998}
.topic-share .social li a.fa-twitter:hover:before{color:#1da1f2}
.topic-share .social li a.fa-pinterest-p:hover:before{color:#cc2127}
.topic-share .social li a.fa-linkedin:hover:before{color:#2384bc}
.topic-share .social li a.fa-whatsapp:hover:before{color:#0D660A}
.topic-share .social li a.fa-envelope:hover:before{color:#7954ad}
.topic-share .social li a.fa-print:hover:before{color:#555555}

/* Post Reactions */
.reaction-buttons{border-top:1px solid $(home.cate.line);display:block;margin:$(main.margin) 0 0;padding-top:$(main.margin)}
.reactions-label{display:inline-block;vertical-align:top;font-weight:700;color:$(home.cate.text)}
iframe.reactions-iframe{height:20px;display:inline-block;vertical-align:sub}
#FancyAllItems *{-moz-transition:none;-webkit-transition:none;transition:none}

/* Topic Author */
.topic-author{display:none;margin-top:$(main.margin);width:100%;margin-$endSide:0;padding:$(main.padding);overflow:hidden;border:1px solid $(home.cate.line);position:relative;border-radius:5px}
.topic-author .author-img{float:$startSide;width:90px;height:90px;margin-$endSide:10px;border-radius:100px;overflow:hidden}
.topic-author .author-img img{width:100%}
.topic-author-name{display:inline-block;margin:0;font-size:15px;background:$(keycolor);color:#FFF;padding:3px 15px;border-radius:100px}
b.author-rank{display:inline-block;margin:5px 0;background-color:$(step.color);font-size:11px;color:#FFF;padding:0 10px;border-radius:100px}
.author-about{font-size:11px;font-weight:600;color:$(home.cate.text);text-align:justify;float:$startSide;width:calc(100% - 100px)}
.topic-author .social{position:absolute;$endSide:15px;top:15px}
.topic-author .social a{width:35px;height:35px;padding-top:9px;margin:0 2px;border-radius:100%;font-size:18px;display:inline-block;text-align:center;color:#FFF;vertical-align:top}
.topic-author .social a svg{fill:#FFF;width:100%;height:15px}
.topic-author .social a:hover{-webkit-animation:SocIcons .2s ease-in-out;animation:SocIcons .2s ease-in-out}
.author-profile:before{content:"\f2bd"}
.author-profile:before{font-family:fontawesome;display:inline-block;margin-$endSide:10px}
.author-profile{float:$endSide;border:2px solid $(home.cate.line);color:$(home.cate.text);font-weight:700;font-size:12px;border-radius:5px;padding:5px 10px;margin-top:10px}
.author-profile:hover{border:2px solid $(keycolor)!important;color:$(keycolor)!important}

/* Navigation */
.topic-nav{margin-top:$(main.margin);display:block;padding:$(main.padding);border:1px solid $(home.cate.line);border-radius:5px}
.topic-nav-wrap{position:relative}
.topic-nav-cont{overflow:hidden;font-size:0}
.topic-nav-cont > a{width:50%;min-height:95px;padding:15px;text-align:center;border-radius:5px;position:relative}
.topic-nav-cont > a:hover{position:static;background:-webkit-gradient(linear,$endSide,from($(keycolor)),to($(step.color)));background:linear-gradient(to $endSide,$(keycolor),$(step.color))}
.topic-nav .next{float:$endSide}
.topic-nav .prev{float:$startSide;border-$endSide:1px solid $(home.cate.line)}
.topic-nav-cont span{cursor:default;display:block;width:120px;margin:0 auto;font-size:12px;color:$(home.cate.text);position:relative;font-weight:700;padding:5px 0;border-radius:100px;background:$(home.cate.line)}
.topic-nav-cont > a:hover span{background-color:#FFF;color:$(keycolor)!important}
.topic-nav-cont b{display:block;font-size:16px;margin:10px 0 0;overflow:hidden;color:$(post.text);max-height:50px;line-height:1.5em}
.topic-nav-cont > a:hover b{color:#FFF}
.topic-nav .topic-img{background-color:$(main.back);opacity:0;width:49.9%;height:180px;border:10px solid $(step.color);border-radius:5px;position:absolute;bottom:150%;z-index:1}
.topic-nav .topic-img img{border-radius:0;width:100%;height:100%}
.topic-nav-cont > a:hover .topic-img{opacity:1;bottom:120%}
.topic-nav .next .topic-img{$endSide:0}
.topic-nav .prev .topic-img{$startSide:0}
.topic-nav .topic-img:after{content:"";display:block;border-width:19px;border-style:solid;border-color:$(step.color) transparent transparent;position:absolute;$endSide:calc(50% - 19px);top:100%}

/* Related Posts */
.related-carousel{padding-bottom:20px}
.related-carousel .Item{width:33.3%;padding:0 7.5px}
.related-carousel .item-wrap{position:relative;padding:15px;border:1px solid $(home.cate.line);border-radius:5px}
.related-carousel .item-wrap:hover{-webkit-box-shadow:0 3px 8px -2px rgba(0,0,0,0.1);box-shadow:0 3px 8px -2px rgba(0,0,0,0.1)}
.topic-related{margin-top:$(main.margin)}
.topic-related .details{margin:10px 0 5px;display:block;border-bottom:1px solid $(home.cate.line)}
.topic-related .details > *{color:$(home.cate.text)}
.related-carousel .img-wrap{width:100%;height:200px}
.topic-related .Item h3{font-size:14px;margin:0;overflow:hidden}
.topic-related .Item h3 a{color:$(home.cate.link)}
.topic-related .Item h3 a:hover{color:$(keycolor)}
.related-carousel .splide__arrows{$endSide:0;top:-55px;position:absolute}
.related-carousel button{position:relative;border-radius:5px;width:30px;height:30px;padding:0;text-align:center;border:none;cursor:pointer;transition:0s;background-color:$(home.cate.line);color:$(home.cate.text);font-size:10px}
.related-carousel button:hover{background:-webkit-gradient(linear,$endSide,from($(keycolor)),to($(step.color)));background:linear-gradient(to $endSide,$(keycolor),$(step.color));color:$(grad.color)}
.related-carousel button.splide__arrow--next{left:0}
.related-carousel button.splide__arrow--prev{left:5px}
.related-carousel button svg{width:15px;position:static;float:none;display:block;margin:0 auto;transition:0s;pointer-events:none}

/* Topic Comments */
.topic-comments{margin-top:$(main.margin);overflow:hidden}
#comment-editor{margin-top:20px}
.comments-bar{display:block;overflow:hidden}
.comments-bar button{height:40px;font-family:inherit;border:none;font-weight:700;float:$startSide;padding:10px 15px;margin-$endSide:10px;border-radius:5px 5px 0 0;background-color:$(home.cate.line);cursor:pointer;-webkit-transform:translate(0,7px);-ms-transform:translate(0,7px);text-shadow:0 2px rgba(0,0,0,.3)}
.comments-bar .active{-webkit-transform:translate(0);-ms-transform:translate(0);color:#FFF}
button[data-bar="face"]:hover,button[data-bar="facebook"]{background-color:#3b5998;color:#FFF}
button[data-bar="disqus"]:hover,button[data-bar="disqus"]{background-color:#2e9fff;color:#FFF}
button[data-bar="blogger"]:hover,button[data-bar="blogger"]{background-color:#f87850;color:#FFF}
.comments-tabs{clear:both}
.comments-tabs>div{background-color:$(main.back);padding:15px 0;text-align:center;border-width:4px 0;border-style:solid;overflow:hidden;display:none}
.comments-tabs .default{display:block}
.comments-tabs .facebook-tab{border-color:#3b5998}
.comments-tabs .disqus-tab{border-color:#2e9fff}
.comments-tabs .blogger-tab{border-color:#f87850;text-align:$startSide}
.comments-info{margin-bottom:15px;overflow:hidden;font-size:12px}
.comments-count{float:$startSide;padding:5px 0;font-size:14px;position:relative;color:$(home.cate.text)}
.go-respond{float:$startSide;padding:5px 15px;margin:0 25px;background-color:$(home.cate.line);color:$(home.cate.text);border-radius:5px}
.comments-show{float:$endSide}
.comments-show button{font-weight:Bold;margin-right:5px;color:$(home.cate.text);background-color:$(home.cate.line);cursor:pointer;display:inline-block;padding-top:10px;padding-$startSide:25px;padding-bottom:10px;padding-$endSide:15px;position:relative;font-family:inherit;border:none;appearance:none;border-radius:5px}
.comments-show .active:before{content:"\f00c";font-family:FontAwesome;position:absolute;top:11px;$startSide:7px}
.comments-show button:hover,.comments-show .active{background:-webkit-gradient(linear,$endSide,from($(keycolor)),to($(step.color)));background:linear-gradient(to $endSide,$(keycolor),$(step.color));color:$(grad.color)!important}
.comment-block{overflow:hidden}
.comments-list{overflow:hidden;border:1px solid $(home.cate.line);border-radius:5px}
.comments-list ul{margin:0;padding:0;list-style:none}
.comments-list .avatar-image-container{float:$startSide;width:72px;height:72px;margin-$endSide:15px;border-radius:5px;overflow:hidden}
.comments-list .avatar-image-container img{width:100%;height:100%;display:block}
.comments-list .comment-replies .avatar-image-container{width:40px;height:40px}
.comments-list .comment-content{line-height:1.5em;margin:0;color:$(home.cate.text);font-size:14px;margin-$startSide:87px}
.comments-list .comment-replies .comment-content{margin-$startSide:57px}
.comments-list cite.user{font-style:normal;display:inline-block;margin:0 0 5px;font-size:13px;position:relative;font-weight:700;background-color:$(home.cate.text);color:$(home.cate.line);padding:0 15px;border-radius:5px}
.comments-list cite.user.blog-author{background:-webkit-gradient(linear,$endSide,from($(keycolor)),to($(step.color)));background:linear-gradient(to $endSide,$(keycolor),$(step.color));color:$(grad.color)}
li.comment{position:relative;overflow:hidden}
.comments-list > ul > li.comment{padding:15px 0;margin:0 15px;overflow:hidden;border-top:1px solid $(home.cate.line)}
.comments-list > ul > li:first-of-type{border-top:none}
.comments-list .comment-replies{float:$startSide;width:100%;padding-$startSide:90px}
.comments-list .comment-replies li.comment{border-top:1px solid $(home.cate.line);margin-top:15px;padding-top:15px}
.comments-list .comment-replies li:last-of-type{padding-bottom:0}
#loadmore{float:$endSide;background-color:$(home.cate.line);margin:0 15px 15px;padding:5px 15px;color:$(home.cate.text);cursor:pointer;overflow:hidden}
#loadmore:hover{box-shadow:0 3px 5px rgba(0,0,0,0.1)}
.thread-toggle,.continue,.comment-replies:empty{display:none!important}
.comment-content img{width:auto;width:100%;height:auto;display:block;position:relative;top:15px;margin-bottom:15px}
.comment-content iframe{width:100%;height:350px;display:block;margin-bottom:30px;top:15px;position:relative}
.comment-content a{color:$(keycolor);text-decoration:underline}
.comment-content a:hover{color:$(step.color)}
.comment-actions{position:absolute;$endSide:0;top:15px}
.comment-actions span>*{float:left;color:$(home.cate.link);padding:7px 12px;background-color:$(home.cate.line);font-size:12px;position:relative;cursor:pointer;border-radius:100px;transition:0s;font-family:inherit;border:none;margin:0;line-height:1.5em;margin-$startSide:5px}
.comment-actions span>*:hover{background:-webkit-gradient(linear,$endSide,from($(keycolor)),to($(step.color)));background:linear-gradient(to $endSide,$(keycolor),$(step.color));color:$(grad.color)!important}
.comment-actions span>*:before{font:normal normal normal 14px FontAwesome;display:inline-block;vertical-align:-2px;margin-$endSide:5px}
.comment-actions span>button:before{content:"\f112"}
.comment-actions span>a:before{content:"\f014"}
#comments-respond{padding:$(main.padding);margin-top:25px;border:1px solid $(home.cate.line);border-radius:5px}
.com-date{display:block;margin-top:-5px;margin-$endSide:15px;font-size:10.2px;color:$(home.cate.text)}
.com-date:before{content:"\f273";font-family:fontawesome;display:inline-block;vertical-align:top;margin-$endSide:5px}
#comments-respond h4{margin:0;color:$(home.cate.link)}
#comments-respond h4:before{content:"\f086";font-family:fontawesome;font-weight:400;color:$(step.color);margin-$endSide:10px;font-size:26px;display:inline-block;vertical-align:text-bottom}
#comments-respond p{margin:0 0 5px;color:$(home.cate.text);padding-$startSide:35px}
.c-not-allowed{font-size:16px;color:$(post.text)}
button.toggle-comments{width:100%;padding:15px;font-family:inherit;font-weight:700;font-size:16px;border:none;background:-webkit-gradient(linear,$endSide,from($(keycolor)),to($(step.color)));background:linear-gradient(to $endSide,$(keycolor),$(step.color));color:$(grad.color);cursor:pointer;border-radius:5px}
/* Error Page
===================*/
.error_page .side-$startSide{float:none;width:100%;margin-bottom:$(main.margin)}
.ErrorSection{border:1px solid $(home.cate.line);padding:20px;text-align:center;border-radius:5px}
.ErrorSection h2{color:$(home.cate.text);margin:38px 0 50px;font-size:64px}
.ErrorSection span{display:block}
.ErrorSection span i{font-size:110px;color:$(keycolor)}
.ErrorSection p{color:$(home.cate.text);margin:20px 0 0;font-size:18px;font-weight:700;text-align:center}

/* Redirect Page
===================*/
#redirect-page{font-style:normal}
.cLoaderWrap{text-align:center;width:200px;margin:0 auto;position:relative;font-style:normal;display:block}
#cLoaderSVG{-webkit-transform:rotate(-90deg);transform:rotate(-90deg);width:200px;height:200px;display:block}
.cLoader{stroke-dashoffset:440;stroke-dasharray:440;-webkit-transition:all 1s linear;transition:all 1s linear;r:70;cy:100;cx:100;fill:none;stroke-width:5px;stroke:$(keycolor)}
.cLoader.done{stroke:$(step.color)}
.cPath{stroke-dashoffset:0;stroke-dasharray:440;r:70;cy:100;cx:100;stroke-width:12px;stroke:$(post.line);fill:none}
.cCount{position:absolute;top:85px;right:calc(50% - 33px);font-size:60px;font-family:Arial!important;display:block;margin-bottom:0px;-webkit-transform:scale(0);transform:scale(0);color:$(step.color)}
.zoom{-webkit-transform:scale(1)!important;transform:scale(1)!important}
#redirect-page .cLoaded{width:180px;height:180px;position:absolute;right:calc(50% - 90px);top:19px;font-size:70px;padding-top:47px;color:$(step.color);-webkit-transform:scale(0);transform:scale(0)}
.cLink.disabled{background:#DDD;color:#999;pointer-events:none}
.cButton{text-align:center}
.cLink{text-decoration:none;line-height:1.5em;background:-webkit-gradient(linear,$endSide,from($(keycolor)),to($(step.color)));background:linear-gradient(to $endSide,$(keycolor),$(step.color));color:$(grad.color);font-weight:bold;border-radius:5px;display:inline-block;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none;padding:10px 30px;font-size:22px}
.cLink:hover{-webkit-box-shadow:0 8px 5px -5px rgba(0,0,0,0.3);box-shadow:0 8px 5px -5px rgba(0,0,0,0.3);-webkit-transform:translateY(-3px);transform:translateY(-3px)}
.redirect-modal{z-index:10;position:fixed;width:100vw;right:0;top:0;height:100vh;display:none;direction:ltr;display:none}
.modal-overlay{background-color:rgba(0,0,0,0.8);width:100%;height:100%;position:fixed;overflow-y:auto}
.modal-content{width:80%;position:relative;direction:unset;overflow:hidden;border-radius:5px;margin:30px auto;max-width:900px;-webkit-box-shadow:0 0 100px #000;box-shadow:0 0 100px #000}
.modal-head{background-color:$(home.cate.line);height:50px;overflow:hidden}
.modal-body{background-color:$(main.back);min-height:calc(100vh - 110px);overflow:hidden;overflow:hidden;padding:30px}
.modal-title{float:$startSide;font-size:20px;margin:14px 30px;font-weight:700}
.fa.modal-close{float:$endSide;background-color:$(keycolor);width:50px;height:50px;text-align:center;color:#FFF;font-size:20px;padding-top:15px;cursor:pointer}
.modal-close:before{display:block}
.modal-close:hover:before{-webkit-transform:scale(1.5);transform:scale(1.5)}

/* Archive Page
===================*/
.post-body .arp-item{overflow:hidden;margin-bottom:10px}
.post-body .arp-thumb{width:72px;height:72px;float:$startSide;margin-$endSide:20px;border-radius:5px}
.post-body .arp-link{text-decoration:none;display:block;font-size:16px;font-weight:700;color:$(home.cate.link)}
.post-body .arp-label-name{background:-webkit-linear-gradient(to $endSide,$(keycolor),$(step.color));background:-o-linear-gradient(to $endSide,$(keycolor),$(step.color));background:-moz-linear-gradient(to $endSide,$(keycolor),$(step.color));background:linear-gradient(to $endSide,$(keycolor),$(step.color));display:block;margin-bottom:10px;padding-top:20px;padding-bottom:20px;padding-$startSide:20px;padding-$endSide:60px;color:$(grad.color);position:relative;border-radius:5px;}
.post-body .arp-label-name b{font-size:26px}
.post-body .arp-label-count{position:absolute;font-size:14px;background-color:rgba(0,0,0,0.1);padding:5px 10px;border-radius:5px;font-weight:700;top:-webkit-calc(50% - 20px);top:-moz-calc(50% - 20px);top:calc(50% - 20px);$endSide:20px}
.post-body .arp-label-count u{text-decoration:none}
.post-body .arp-date,.arp-cate{display:inline-block;vertical-align:top;font-size:10px;padding:0 10px;border-radius:300px;margin-bottom:8px;font-weight:700;line-height:2em}
.post-body .arp-date{color:$(grad.color);background-color:$(keycolor);border:2px solid $(keycolor);margin-$endSide:5px}
.post-body .arp-cate{color:$(step.color);border:2px solid $(step.color)}
.post-body .arp-link:before{content:"";width:0;height:2px;border-radius:100px;background:$(step.color);vertical-align:middle;display:inline-block}
.post-body .arp-link:hover{color:$(keycolor)}
.post-body .arp-link:hover:before{width:15px;margin-$endSide:5px}

/* Authors Page
===================*/
.aup-wrapper{margin:0 auto;width:70%}
.aup-head{width:166px;height:166px;margin:0 auto 20px;background:-webkit-linear-gradient(to $endSide,$(keycolor),$(step.color));background:-o-linear-gradient(to $endSide,$(keycolor),$(step.color));background:-moz-linear-gradient(to $endSide,$(keycolor),$(step.color));background:linear-gradient(to $endSide,$(keycolor),$(step.color));border-radius:200px}
.aup-photo{width:160px;height:160px;border-radius:200px;top:3px;$startSide:3px;border:10px solid $(main.back);position:relative;-moz-background-size:cover;background-size:cover}
.aup-name{text-align:center;background:-webkit-linear-gradient(to $endSide,$(keycolor),$(step.color));background:-moz-linear-gradient(to $endSide,$(keycolor),$(step.color));background:-o-linear-gradient(to $endSide,$(keycolor),$(step.color));background:linear-gradient(to $endSide,$(keycolor),$(step.color));-webkit-background-clip:text;-webkit-text-fill-color:transparent;margin:0 auto 10px}
.aup-title{text-align:center;display:block}
.aup-title b{background-color:$(step.color);display:inline-block;padding:2px 20px;border-radius:100px;color:$(grad.color)}
.aup-about{margin:5px auto;font-size:13px;color:$(home.cate.text)}
.aup-social{text-align:center}
.aup-social a{font-size:14px;width:30px;height:30px;margin:0 2px;line-height:30px;color:#FFF!important;text-decoration:none!important;border-radius:100%;vertical-align:top}
.aup-social a svg{fill:#FFF;width:100%;height:14px;margin-top:8px}
.timeline-item{padding:10px 0;position:relative;overflow:hidden;height:102px}
.timeline-date{float:$startSide;vertical-align:middle;width:100px;line-height:2em;padding-top:15px}
.timeline-date b{display:block;text-align:center;font-size:40px;color:$(step.color)}
.timeline-date i{text-align:center;display:block;font-style:normal;font-size:12px;color:$(home.cate.text)}
.timeline-point{display:inline-block;width:5px;background:$(home.cate.line);height:100%;position:absolute;$startSide:120px;top:0}
.timeline-point:before{content:'';width:21px;height:21px;display:block;background:$(keycolor);top:-webkit-calc(50% - 11.5px);top:-moz-calc(50% - 11.5px);top:calc(50% - 11.5px);position:relative;$startSide:-8px;border:5px solid $(main.back);border-radius:100%}
.timeline-post{padding-$startSide:60px;float:$startSide;width:-webkit-calc(100% - 100px);width:-moz-calc(100% - 100px);width:calc(100% - 100px)}
.post-body img.timeline-thumb{width:82px;height:82px;display:inline-block;vertical-align:middle;border-radius:100px;border:5px solid $(home.cate.line);-webkit-box-shadow:0 0 1px 0 $(home.cate.text);-moz-box-shadow:0 0 1px 0 $(home.cate.text);box-shadow:0 0 1px 0 $(home.cate.text)}
.post-body a.timeline-title{text-decoration:none;display:inline-block;vertical-align:middle;margin-$startSide:20px;font-weight:700;font-size:16px;color:$(home.cate.link);width:-webkit-calc(100% - 102px);width:-moz-calc(100% - 102px);width:calc(100% - 102px);line-height:1.6em;max-height:75px;overflow:hidden}
.timeline-item:hover .timeline-point:before{background-color:$(step.color);-webkit-transform:scale(1.5);-ms-transform:scale(1.5);-moz-transform:scale(1.5);-o-transform:scale(1.5);transform:scale(1.5)}
.timeline-post:hover a{color:$(keycolor)}
.timeline-post:hover .timeline-thumb{-webkit-animation:thumb .5s ease-out;-moz-animation:thumb .5s ease-out;-o-animation:thumb .5s ease-out;animation:thumb .5s ease-out}
@-webkit-keyframes thumb{
25%{-webkit-transform:rotate(-30deg);transform:rotate(-30deg)}
75%{-webkit-transform:rotate(10deg);transform:rotate(10deg)}
}
@-moz-keyframes thumb{
25%{-webkit-transform:rotate(-30deg);-moz-transform:rotate(-30deg);transform:rotate(-30deg)}
75%{-webkit-transform:rotate(10deg);-moz-transform:rotate(10deg);transform:rotate(10deg)}
}
@-o-keyframes thumb{
25%{-webkit-transform:rotate(-30deg);-o-transform:rotate(-30deg);transform:rotate(-30deg)}
75%{-webkit-transform:rotate(10deg);-o-transform:rotate(10deg);transform:rotate(10deg)}
}
@keyframes thumb{
25%{-webkit-transform:rotate(-30deg);-moz-transform:rotate(-30deg);-o-transform:rotate(-30deg);transform:rotate(-30deg)}
75%{-webkit-transform:rotate(10deg);-moz-transform:rotate(10deg);-o-transform:rotate(10deg);transform:rotate(10deg)}
}
.timeline-month{position:relative}
.timeline-month > .timeline-point{height:70px}
.timeline-month-name{margin-$startSide:140px;height:70px}
.timeline-month-name span{line-height:1.5em;display:inline-block;color:$(home.cate.text);padding:5px 20px;border-radius:100px;font-weight:700;margin-top:20px;border:2px solid $(home.cate.line)}
.timeline-month > .timeline-point:before{background-color:$(home.cate.line);-webkit-transform:scale(1.5);-ms-transform:scale(1.5);-moz-transform:scale(1.5);-o-transform:scale(1.5);transform:scale(1.5)}
.blog-author-card{width:260px;display:inline-block;vertical-align:top;margin:0 10px 10px 0;border:2px solid $(home.cate.line);padding:20px;overflow:hidden;border-radius:10px}
.blog-authors{text-align:center}
b.blog-author-name{display:block;font-size:18px;border-bottom:2px solid $(step.color);color:$(step.color);padding-bottom:10px}
.blog-author-avatar{display:block;margin:10px auto;width:130px;height:130px;border-radius:100px;-moz-background-size:100% 100%;background-size:100% 100%;border:5px solid $(main.back);-webkit-box-shadow:0 0 0 3px $(keycolor);-moz-box-shadow:0 0 0 3px $(keycolor);box-shadow:0 0 0 3px $(keycolor)}
.blog-author-rank{background-color:$(keycolor);font-size:12px;display:inline-block;padding:0px 15px;border-radius:100px;margin-bottom:10px;color:#FFF}
.blog-author-social a{text-decoration:none;display:inline-block;vertical-align:top;margin:2px;border-radius:5px;width:25px;height:25px;line-height:24px;color:$(home.cate.text);border:1px solid $(home.cate.line)}
.blog-author-social a svg{fill:$(home.cate.text);width:100%;height:13px;margin-top:5px;transition:0s}
.blog-author-social a:before{-webkit-transition:none;-o-transition:none;-moz-transition:none;transition:none}
.blog-author-social a:hover{border:1px solid $(step.color);background-color:$(step.color);color:$(main.back)}
.blog-author-social a:hover svg{fill:$(main.back)}
b.blog-author-count{float:$startSide;font-size:12px;color:$(home.cate.link)}
b.blog-author-link{float:$endSide}
.blog-author-social{min-height:30px;margin-bottom:10px}
.post-body a.blog-author-link{text-decoration:none;line-height:2em;float:$endSide;color:$(step.color);padding:2px 15px;border-radius:100px;font-size:11px;font-weight:700;border:2px solid $(step.color)}
.post-body .blog-author-link:hover{background-color:$(step.color);color:#FFF}
b.blog-author-count:before{content:'';width:10px;height:5px;display:inline-block;background-color:$(keycolor);border-radius:100px;margin-$endSide:5px;vertical-align:2px}

/* Shortcodes
===================*/
/* Premium Content */
.post-body .premium{display:none;border:10px solid $(main.back);margin:20px auto;padding:20px;-webkit-box-shadow:0 0 2px 0 rgba(0,0,0,0.3);-moz-box-shadow:0 0 2px 0 rgba(0,0,0,0.3);box-shadow:0 0 2px 0 rgba(0,0,0,0.3);text-align:center;width:70%;position:relative;line-height:2em;background-color:$(home.cate.line);line-height:25px;border-radius:5px}
.post-body .prm-title{margin-top:0px;margin-bottom:0px;margin-$startSide:20px;padding-$endSide:0px;font-size:20px;color:$(home.cate.link);text-align:$startSide}
.post-body .prm-title:before{content:"\f023";font-family:fontawesome;margin:0 15px;font-weight:400;color:$(home.cate.text);font-size:35px;background-color:$(home.cate.line);width:60px;height:60px;padding-top:12px;border-radius:100px;border:5px solid $(keycolor);display:inline-block;vertical-align:-17px;-webkit-box-shadow:0 0 0 10px $(home.cate.line);-moz-box-shadow:0 0 0 10px $(home.cate.line);box-shadow:0 0 0 10px $(home.cate.line);text-align:center}
.post-body .prem-desc{display:block;color:$(grad.color);font-weight:700;font-size:12px;background:-webkit-linear-gradient(to $endSide,$(keycolor),$(step.color));background:-o-linear-gradient(to $endSide,$(keycolor),$(step.color));background:-moz-linear-gradient(to $endSide,$(keycolor),$(step.color));background:linear-gradient(to $endSide,$(keycolor),$(step.color));width:100%;padding-top:5px;padding-bottom:5px;padding-$startSide:110px;padding-$endSide:0px;margin:-22px 0 20px;text-align:$startSide;line-height:1.5em;    border-radius:5px}
.post-body .pr-but{text-decoration:none;padding:5px 10px;display:inline-block;vertical-align:middle;margin:0 5px 10px;border-radius:5px;color:#FFF;font-family:inherit;direction:ltr;text-align:center;font-weight:700}
.post-body .pr-but:hover{color:#FFF;-webkit-box-shadow:0 2px 5px 0 rgba(0,0,0,0.2);-moz-box-shadow:0 2px 5px 0 rgba(0,0,0,0.2);box-shadow:0 2px 5px 0 rgba(0,0,0,0.2);-webkit-transform:translateY(-3px);-moz-transform:translateY(-3px);-ms-transform:translateY(-3px);-o-transform:translateY(-3px);transform:translateY(-3px)}
.post-body .pr-but.pr-but-facebook:before{content:"\f09a"}
.post-body .pr-but.pr-but-twitter:before{content:"\f099"}
.post-body .pr-but.pr-but-google:before{content:"\f0d5"}
.post-body .pr-but.pr-but-facebook{background-color:#3b5998}
.post-body .pr-but.pr-but-twitter{background-color:#1da1f2}
.post-body .pr-but.pr-but-google{background-color:#dd4b39}
.post-body .pr-but:before{font-family:fontawesome;display:inline-block;vertical-align:middle;font-weight:400;margin-right:10px;padding-right:10px;font-size:18px;border-right:1px solid rgba(255,255,255,0.1)}
.hltd{-webkit-transition:none;-o-transition:none;-moz-transition:none;transition:none;-webkit-animation:hltd .5s linear;-moz-animation:hltd .5s linear;-o-animation:hltd .5s linear;animation:hltd .5s linear}
@-webkit-keyframes hltd{
from{background-color:#ffa}
to{background-color:transparent}
}
@-moz-keyframes hltd{
from{background-color:#ffa}
to{background-color:transparent}
}
@-o-keyframes hltd{
from{background-color:#ffa}
to{background-color:transparent}
}
@keyframes hltd{
from{background-color:#ffa}
to{background-color:transparent}
}

/* Messages */
.post-body i.msgs{display:block;padding-bottom:15px;padding-top:15px;padding-$startSide:50px;padding-$endSide:50px;font-style:normal;border-radius:5px;font-weight:bold;line-height:1.5em;position:relative;border-width:1px;border-style:solid;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}
.post-body i.msgs:before{font-family:fontawesome;width:30px;height:30px;border-radius:100px;text-align:center;margin-$endSide:10px;color:#FFF;font-weight:normal;font-size:22px;line-height:30px;position:absolute;$startSide:10px;top:-webkit-calc(50% - 15px);top:-moz-calc(50% - 15px);top:calc(50% - 15px)}
.post-body i.msgs.info:before{content:"\f129";background-color:#68c9ff}
.post-body i.msgs.success:before{content:"\f00c";background-color:#46ea77}
.post-body i.msgs.error:before{content:"\f00d";background-color:#f58282}
.post-body i.msgs.warning:before{content:"\f12a";background-color:#d6c137}
.post-body i.msgs.gift:before{content:"\f06b";background-color:#c775c3}
.post-body i.msgs.info{border-color:#68c9ff;color:#68c9ff;background-color:#e4f5ff}
.post-body i.msgs.success{border-color:#46ea77;color:#46ea77;background-color:#e4fff5}
.post-body i.msgs.error{border-color:#f58282;color:#f58282;background-color:#ffe4e4}
.post-body i.msgs.warning{border-color:#d6c137;color:#d6c137;background-color:#fff8e4}
.post-body i.msgs.gift{border-color:#c775c3;color:#c775c3;background-color:#ffe4f9}

/* Buttons */
.post-body a.sq-button{text-decoration:none;line-height:1.5em;background:-webkit-linear-gradient(to $endSide,$(keycolor),$(step.color));background:-o-linear-gradient(to $endSide,$(keycolor),$(step.color));background:-moz-linear-gradient(to $endSide,$(keycolor),$(step.color));background:linear-gradient(to $endSide,$(keycolor),$(step.color));color:$(grad.color);font-weight:bold;border-radius:5px;display:inline-block;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}
.post-body a.sq-button.sm{padding:2px 15px}
.post-body a.sq-button.md{padding:5px 15px;font-size:16px}
.post-body a.sq-button.lg{padding:5px 15px;font-size:20px}
.post-body a.sq-button.xl{padding:10px 30px;font-size:22px}
.post-body a.sq-button:hover{-webkit-box-shadow:0 8px 5px -5px rgba(0,0,0,0.3);-moz-box-shadow:0 8px 5px -5px rgba(0,0,0,0.3);box-shadow:0 8px 5px -5px rgba(0,0,0,0.3);-webkit-transform:translateY(-3px);-ms-transform:translateY(-3px);-moz-transform:translateY(-3px);-o-transform:translateY(-3px);transform:translateY(-3px)}

/* Dev Code */
.post-body pre.sq-code{direction:ltr;width:100%;display:block;font-size:0;line-height:30px;overflow:auto}
.post-body .code-sn{display:inline-block;width:40px;color:$(grad.color);font-weight:bold;text-align:center;background-color:$(keycolor);font-size:14px;-webkit-user-select:none;-moz-user-select:none;-ms-user-select:none;user-select:none}
.post-body .code-sn span{display:block}
.post-body .code-sn span:nth-of-type(odd){background-color:rgba(255,255,255,0.1)}
.post-body .sq-source{margin:0;display:inline-block;vertical-align:top;background-color:$(home.cate.line);color:$(home.cate.link);width:calc(100% - 40px);font-size:12px;text-align:left}
.post-body .sq-source code{display:block;padding:0 10px}
.post-body .sq-source code:nth-of-type(odd){background-color:rgba(0,0,0,0.05)}

/* Contact Form */
.post-body .ContactForm input[type='text'],.post-body .ContactForm textarea{border-bottom:2px solid $(home.cate.line);color:$(home.cate.text)}
.post-body .ContactForm i{color:$(home.cate.text)}
.post-body .ContactForm b{position:absolute;$startSide:30px;color:$(home.cate.text)}
.post-body .contact-state{float:$startSide;color:$(home.cate.text)}

/* Float Toggles
===================*/
.b-toggles{position:fixed;top:150px;$startSide:-60px;z-index:9999999;padding:15px;border-radius:0 0 0 10px;border:1px solid $(home.cate.line);background-color:$(main.back)}
.b-toggles span{position:absolute;$startSide:100%;top:-1px;background-color:inherit;padding-top:5px;width:30px;height:30px;text-align:center;color:#000;border:inherit;border-$startSide:none;border-radius:3px 0 0 3px;cursor:pointer;transition:inherit}
.b-toggles span:before{content:'\f013';font-family:fontAwesome}
.b-toggles.open{$startSide:0}
.b-toggles button{position:relative;width:40px;height:40px;border:none;font-size:24px;display:flex;justify-content:center;align-items:center;cursor:pointer;background:none;opacity:0.9;font-family:inherit}
.b-toggles button:hover{opacity:1}
.b-toggles button:after{content:attr(title);position:absolute;$startSide:120%;background-color:#3e3e3e;white-space:nowrap;font-family:inherit;font-size:12px;padding:7px 12px 9px;border-radius:5px;font-weight:normal;color:#FFF;transform:translateX(-20px);opacity:0;pointer-events:none}
.b-toggles button:before{content:'';border-top:5px solid transparent;border-bottom:5px solid transparent;border-$endSide:5px solid #3e3e3e;position:absolute;$startSide:110%;transform:translateX(-20px);opacity:0}
.b-toggles button:hover:before,.b-toggles button:hover:after{transform:translateX(0);opacity:1}
.b-toggles svg, .b-toggles i{width:30px;height:30px;display:block;fill:$(keycolor);color:$(keycolor);display:none}
body:not(.dm) #bt-scheme svg:first-of-type{display:block}
body.dm #bt-scheme svg:last-of-type{display:block}
body:not(.boxed) #bt-boxing i:first-of-type{display:block}
body.boxed #bt-boxing i:last-of-type{display:block}
#bt-install i{display:block}

/* NightMode
===================*/
.dm .static-page .entry-title,.dm .quickedit:after{color:#FFF}
/* MAIN */
.dm .main-container,.dm a.next-page, .dm a.prev-page,.dm .modal-body,.dm .Loading,.dm .comments-tabs>div, .dm .topic-nav .topic-img, .dm .b-toggles{background-color:$(dm.main)}
.dm .blog-author-avatar,.dm .aup-photo,.dm .timeline-month > .timeline-point:before{border-color:$(dm.main)}
.dm.rtl a.prev-page{-webkit-box-shadow:10px 0 0, -20px 0 0 $(dm.main);box-shadow:10px 0 0, -20px 0 0 $(dm.main)}
.dm.ltr a.prev-page{-webkit-box-shadow:-10px 0 0, 20px 0 0 $(dm.main);box-shadow:-10px 0 0, 20px 0 0 $(dm.main)}
.dm.rtl a.next-page{-webkit-box-shadow:-10px 0 0, 20px 0 0 $(dm.main);box-shadow:-10px 0 0, 20px 0 0 $(dm.main)}
.dm.ltr a.next-page{-webkit-box-shadow:10px 0 0, -20px 0 0 $(dm.main);box-shadow:10px 0 0, -20px 0 0 $(dm.main)}
/* MENUS */
.dm header .color-wrap,.dm #LinkList302,.dm .ticker-title,.dm #footer,.dm .post-body .sq-source,.dm .drop-menu-st > ul,.dm .bot-menu-st > ul,.dm .mega-wrap,.dm #menu-bar .menu-bar > ul,.dm .menu-res-wrap ul{background:$(dm.menu)}
/* TRACKS */
.dm .ticker,.dm article .topic-tools,.dm header #top-bar .menu li a,.dm .mega-carousel .splide__arrows button,.dm .scroll-top{background-color:$(dm.track)}
/* LINKS */
.dm .headline h2 a,.dm .ticker-content li a,.dm .cate-link a,.dm .main-wrap .headline h4,.dm .main-wrap .headline h2,.dm .main-wrap aside .headline h4,.dm .rand-content h3 a,.dm .LinkList .widget-content li a,.dm * .PopularPosts .post-title a,.dm .headline h4,.dm .FeaturedPost h3,.dm .PageList .widget-content li a,.dm .list-label-widget-content .label-name,.dm #TOC li a,.dm #HTML303,.dm #TOC > span,.dm .post-body a.timeline-title,.dm .recent-comments .comment p,.dm .post-body .arp-link,.dm .comments-show button,.dm .toggle-comments,.dm .go-respond,.dm #menu-bar .menu-bar ul li>a,.dm .ErrorSection h2,.dm header .search input,.dm .ContactForm input[type='text'],.dm .ContactForm textarea,.dm .status-msg-body b,.dm .bot-menu-st:after,.dm .drop-menu-st:after,.dm #Pagination span,.dm #Pagination button,.dm header #top-bar .menu li a,.dm .topic-nav-cont b,.dm .topic-related .Item h3 a,.dm .toggle-comments,.dm .go-respond,.dm .comment-actions span *,.dm .comments-list .comment-content,.dm #comments-respond h4,.dm .subscrib-sec input[name="email"],.dm .author-profile,.dm .mega-post .post-title a,.dm .post-body a.d-link,.dm .modal-head,.dm .menu-res-wrap li a,.dm .BlogArchive .flat .archivedate a,.dm .headone{color:$(dm.link)}
/* TEXT */
.dm .post-body h3, .dm .post-body h2, .dm .post-body h4,.dm header #top-bar .menu li a:hover,.dm header #top-bar .search form span,.dm .cate-slideshow .Item.s-active,.dm .label-size,.dm .status-msg-body,.dm .timeline-month-name span,.dm .timeline-date i,.dm .aup-about,.dm .acc-body,.dm .social-widget li div,.dm .recent-comments .comment .comm-author,.dm .recent-comments .comment .leave-comm,.dm .post-body .prm-title,.dm .post-body .prm-title:before,.dm .details > *,.dm .cate-snippet,.dm .main-slider .m-slider .caption p,.dm .list-label-widget-content .label-count,.dm .headline > a,.dm .ticker-title,.dm .ErrorSection p,.dm .blog-author-social a,.dm b.blog-author-count,.dm .ContactForm i,.dm .ContactForm label,.dm .topic-nav-cont span,.dm .com-date,.dm #comments-respond p,.dm .comments-count,.dm .contact-state p,.dm .subscrib-sec label,.dm * .splide__arrow,.dm * .snippet-item,.dm #TOC>span:before,.dm #TOC li a:before,.dm .post-body,.dm .post-body p,.dm .author-about,.dm .topic-details > *,.dm .zooming b,.dm .post-body .sq-source,.dm .post-body li,.dm .cLink.disabled,.dm .modal-body,.dm .cate-carousel .splide__arrows button,.dm .text-counter-wrapper,.dm .flat .archivedate .post-count,.dm .TextList .widget-content li,.dm #Header1 p, .dm .b-toggles span{color:$(dm.text)}
/* LINES BACK */
.dm .headline > a,.dm .social-widget li div,.dm .CusWidget .v-carousel .vc-nav button,.dm .acc-body,.dm #Pagination span,.dm #Pagination button,.dm .cate-slideshow .Item,.dm .details-on-img .author-prof,.dm .details-on-img .post-date,.dm #TOC li a,.dm .post-body h3, .dm .post-body h2, .dm .post-body h4,.dm #TOC > span,.dm .timeline-point,.dm .timeline-month > .timeline-point:before,.dm .post-body .premium,.dm .comments-show button,.dm .toggle-comments,.dm .go-respond,.dm .cate-carousel .splide__pagination__page,.dm .post-pages:before,.dm .topic-nav-cont span,.dm .comment-actions span *,.dm .subscrib-sec input[name="email"],.dm .BlogSearch .search-input input,.dm * .splide__arrow,.dm .post-body .prm-title:before,.dm .modal-head,.dm .cLink.disabled{background-color:$(dm.shade)}
.dm .cPath{stroke:$(dm.shade)}
.dm .PageList .widget-content li a::before,.dm .list-label-widget-content .label-name::before,.dm .BlogArchive .flat .archivedate a::before,.dm .TextList .widget-content li::before{color:$(dm.shade)}
/* BORDERS */
.dm .headline,.dm .home-cate .widget-content,.dm .cate-sided .Item:nth-of-type(n+2),.dm .cate-sided .Item:first-of-type,.dm .cate-cover .Item:nth-of-type(n+2),.dm .index-posts .post-outer,.dm .main-wrap aside .headline,.dm .social-widget,.dm .CusWidget .v-carousel,.dm .rand-content,.dm .rand-content div,.dm .recent-comments .comment,.dm .comments-img-wrap,.dm .cate-video .Item,.dm #TOC nav,.dm .post-body h3, .dm .post-body h2, .dm .post-body h4,.dm .see-also,.dm .see-also li,.dm .topic-share .social,.dm .topic-author,.dm .topic-nav,.dm .related-carousel .item-wrap,.dm .comments-list,.dm .comments-list > ul > li.comment,.dm .comments-list .comment-replies li.comment,.dm #comments-respond,.dm .author-profile,.dm .topic-related .details,.dm .status-msg-body,.dm .ErrorSection,.dm .blog-author-card,.dm .blog-author-social a,.dm .post-body img.timeline-thumb,.dm .timeline-month-name span,.dm .timeline-point:before,.dm .post-body .premium,.dm .post-body #ContactForm93,.dm .ContactForm input[type='text'],.dm .ContactForm textarea,.dm .topic-nav .prev,.dm .list-label-widget-content .label-name,.dm .PageList .widget-content li a,.dm #footer-top-section:not(.no-items),.dm .PopularPosts article,.dm .BlogArchive .flat .archivedate a,.dm .TextList .widget-content li{border-color:$(dm.border)}
.dm .social-widget li div:after,.dm .b-toggles{border-color:transparent transparent $(dm.border)}

/* Responsive
===================*/
@media screen and (max-width:1200px){#bt-boxing{display:none!important}}
@media screen and (max-width:1050px){
#Header1,#HTML302,#HTML307{float:none;width:100%;text-align:center;margin:0 auto;min-height:auto}
div#Header1{margin-bottom:$(main.margin)}
.aup-wrapper{width:90%}
#footer-sections{flex-wrap:wrap}
#footer-sections .f-sec{width:50%;margin-bottom:$(main.margin)}
}
@media screen and (min-width:481px) and (max-width:1080px){
.cate-video .free-width .Item:nth-of-type(-n+3){margin:0;padding:0;border:0}
.cate-video .free-width .Item:nth-of-type(3n-1){margin:0}
.cate-video .free-width .Item{width:calc((100% - 15px)/2)}
.cate-video .free-width .Item:nth-of-type(n+3){margin-top:$(main.margin);padding-top:$(main.margin);border-top:1px solid $(home.cate.line)}
.cate-video .free-width .Item:nth-of-type(odd){margin-$endSide:15px}
.cate-video .img-wrap{width:100%;margin:0 0 10px;height:150px}
.cate-video .cate-link{width:100%}
}
@media screen and (min-width:481px) and (max-width:860px){
.cate-video .img-wrap{float:none;clear:both;width:100%;height:30vw}
.cate-video .cate-link{margin-top:15px}
}
@media screen and (min-width:992px){
body{background:$(body.background);background-size:cover}
}
@media screen and (max-width:992px){
.stickysides .side-right,.stickysides aside{position:static}
header #top-bar #LinkList301{max-width:calc(100% - 90px);float:$startSide;margin-$startSide:40px;margin-$endSide:0}
.bot-menu-st > ul{$startSide:0}
header #top-bar #PageList301{width:200px;position:absolute;$startSide:0;overflow:visible}
header #top-bar .menu{display:none}
header #top-bar .menu-res{display:block}
header #top-bar #HTML301{width:30px;min-width:auto}
header .search label{position:absolute;$endSide:30px;top:0}
header .search.open-search label{width:200px}
a.res-home{display:block}
#menu-bar .menu-bar ul li>a.home{display:none}
.menu-bar-res{display:block}
#menu-bar .menu-bar > ul{position:absolute;$endSide:0;top:58px;background-color:$(menu.back);width:220px;max-height:0;overflow:auto!important;padding:0 15px;z-index:2;-webkit-box-shadow:0 0 10px rgba(0,0,0,0.5);box-shadow:0 0 10px rgba(0,0,0,0.5)}
#menu-bar .menu-bar > ul.open{max-height:340px;z-index:2;padding:15px}
#menu-bar .menu-bar .drop-menu-st ul{display:none;background-color:rgba(0,0,0,0.2);position:relative;top:0;-webkit-box-shadow:none;box-shadow:none;width:auto}
#menu-bar .menu-bar .drop-menu-st ul li a{font-size:12px}
#menu-bar .menu-bar ul li>a{display:block;padding:10px;font-weight:700;text-align:$startSide;border-bottom:1px dashed rgba(255,255,255,0.075)}
#menu-bar .menu-bar > ul.open > li:last-of-type a{border-bottom:none;}
#menu-bar .menu-bar .drop-menu-st:after{top:10px}
.rtl .bot-menu-st:after,.ltr .bot-menu-st:after{content:"\f078"}
.blockClass{display:block!important;}
.menu-bar li.expanded:after{content:"\f077";}
#menu-bar .menu-bar li.expanded > ul{display:block}
#menu-bar .menu-bar ul li{float:none}
.middle-content{flex-wrap:wrap}
.side-$startSide,aside{width:100%;margin:0!important}
.CusWidget .slider-carousel .img-wrap,.CusWidget .v-carousel .img-wrap{height:35vh}
.details>a,.details div,.details>span,.topic-details a,.go-respond{display:inline-block;padding:8px 0;}
.b-toggles button:before,.b-toggles button:after{display:none}
}
@media screen and (min-width:641px) and (max-width:992px){
.wide-sec .sided-sections.three-cols .section:first-of-type{float:none;width:100%;clear:both;width:100%}
.wide-sec .sided-sections.three-cols .section:nth-of-type(2){margin:0 0 0 15px}
.wide-sec .sided-sections.three-cols .section:nth-of-type(n+2){width:calc((100% - 15px)/2)}
.wide-sec .sided-sections.two-cols .section{margin-$endSide:15px;width:calc((100% - 15px)/2)}
.wide-sec .sided-sections.two-cols .section:last-of-type{margin:0}
}
@media screen and (max-width:860px){
.main-slider .m-slider{float:none;width:100%;margin-bottom:$(main.margin)}
.main-slider .left-box{float:none;width:100%;clear:both;height:auto}
.main-slider .left-box > div{float:$startSide;width:49%}
.main-slider .left-box > div:first-of-type{margin-$endSide:2%}
.topic-tools.zooming{display:block;margin:0 auto}
.topic-tools{border-radius:0 0 5px 5px}
.topic-details{width:95%;margin:0 2.5%;padding:35px $(main.padding) 10px;margin-top:-3px}
.cate-cover .free-width .Item:nth-of-type(n+2){width:100%;float:none}
.v-index .index-posts .post-outer{width:50%}
}
@media screen and (max-width:640px){
.topic-title{border-radius:5px}
body .sided-sections .section{float:none!important;width:100%!important;margin-$endSide:0!important;margin-$startSide:0!important}
#sidebar-section .widget{width:100%;float:none}
#footer #footer-sections .f-sec{width:100%}
.free-width .slideshow-thumbnail,.tight-width .slideshow-thumbnail{float:none;width:100%;height:320px;margin-$endSide:0;margin-bottom:$(main.padding)}
.cate-slideshow .free-width .slideshow-thumbs{float:none;width:100%}
.cate-slideshow .free-width .Item,.cate-slideshow .tight-width .Item{height:auto}
.cate-slideshow .free-width .Item.s-active:after, .cate-slideshow .tight-width .Item.s-active:after{display:none}
.index-posts .img-wrap{width:180px;height:180px;margin-$endSide:15px}
.blog-author-card{margin-$startSide:0}
.aup-wrapper{width:100%}
.topic-author .social{position:relative;$endSide:0;top:0;text-align:$endSide}
#LinkList304{float:none;clear:both;margin:0 auto;text-align:center}
#HTML303{float:none;clear:both;text-align:center;margin-bottom:10px}
.premium{width:98%}
.comments-list .comment-replies{padding-$startSide:0}
.comments-list .comment-replies .comment-content{margin-top:0}
.comment-actions{margin-top:10px;position:relative;top:auto;$endSide:auto;display:block}
.comment-actions span{float:$endSide}
.comment-actions span:first-child{margin-bottom:5px}
.comment-actions span:last-child{margin-$endSide:0}
.post-body .premium{width:100%}
.post-body .prem-desc{margin:10px 0;padding:10px;text-align:center}
.post-body .prm-title{margin:0;text-align:center}
.post-body .prm-title:before{display:block;margin:0 auto}
.cate-cover .free-width .Item:first-of-type .img-wrap{float:none;width:100%;margin-bottom:$(main.padding)}
.scroll-top{top:-30px}
}
@media screen and (max-width:480px){
.ticker-title{font-size:0;padding:0 25px;width:0}
header #top-bar #HTML301{min-width:auto;width:200px;position:absolute;$endSide:$(main.padding)}
.slideshow-thumbnail{height:240px}
.cate-slideshow .widget-content{padding:0;border:none}
.main-slider .left-box > div{width:100%;float:none}
.main-slider .m-slider{height:320px}
.v-index .index-posts .post-outer{width:100%}
.index-posts .post-outer .img-wrap{float:none;width:100%;height:50vw;margin-bottom:15px}
.post-outer h2.post-title{display:block;width:100%}
.comments-bar button{float:none;width:100%;display:block;-webkit-transform:translate(0);transform:translate(0);margin-bottom:5px}
.comments-list .comment-content{clear:both;margin:40px 0px 0}
.comments-list .comment-replies .comment-content{margin:5px 0 0}
.topic-nav-cont .next,.topic-nav-cont .prev{float:none;width:100%;border:none;display:block}
.topic-nav .topic-img{display:none!important}
.arp-label-count u{display:none}
.arp-link{font-size:14px}
.arp-label-name b{font-size:20px}
.timeline-mohth-name{margin-$startSide:110px}
.timeline-date{width:75px}
.timeline-point{$startSide:90px}
.timeline-post{padding-$startSide:40px;width:calc(100% - 75px)}
.timeline-title{margin-$startSide:10px}
.topic-author{text-align:center}
.author-about{float:none;display:block;width:100%;clear:both;margin-bottom:10px;text-align:center}
.topic-author .author-img{display:block;margin:0 auto 10px;float:none}
.topic-author .social{text-align:center}
.author-profile{float:none;display:inline-block}
.separator a{margin:0 auto!important}
.cate-video .Item,.cate-video .free-width .Item{float:none;width:100%;margin:20px 0 0!important;margin-$endSide:0;border-top:1px solid #EEE;padding-top:$(main.padding)}
.cate-video .Item:first-of-type,.cate-video .free-width .Item:first-of-type{margin-top:0!important;padding-top:0;border-top:none}
.cate-video .img-wrap{width:100%;height:50vw;margin:0 0 10px 0}
.cate-video .cate-link{max-height:100%;clear:both}
.comments-tabs .go-respond{margin-bottom:30px;padding:8px}
.comments-tabs .comments-show{float:none;text-align:center;clear:both;font-size:0}
.comments-tabs .comments-show button{width:50%;font-size:12px}
.comments-only{margin-bottom:10px}
.post-body img.timeline-thumb{width:60px;height:60px;border-width:3px}
.post-body a.timeline-title{margin-right:10px;font-size:14px;width:calc(100% - 75px)}
}
@media screen and (max-width:360px){
.home-cate .widget-content,.index-posts{padding:0;border:none}
.timeline-mohth-name{margin-$startSide:30px}
.timeline-item{height:auto}
.timeline-date{width:100%;text-align:$startSide;padding:0 30px 0 0}
.timeline-date b{display:inline-block;font-size:20px;margin-$endSide:5px;vertical-align:middle}
.timeline-date i{display:inline-block;font-size:12px;vertical-align:middle}
.timeline-point{$startSide:10px}
.timeline-post{padding-$startSide:30px;width:100%}
.timeline-thumb{display:none}
a.timeline-title{display:block;margin-$startSide:0;width:100%}
header #top-bar #HTML301{width:150px}
.premium{width:98%}
h6.prm-title{margin:0 0 20px;text-align:center}
span.prem-desc{margin-top:5px;padding:5px 10px;text-align:center}
.prm-title:before{display:block;-webkit-box-shadow:none;box-shadow:none;margin:0 auto}
a.pr-but{display:block;text-align:$endSide}
.pr-but:before{width:30px}
}
@media print{
.main-container>header,.intro,footer,aside,.topic-share,.topic-author,.topic-nav,.topic-related,div[id^='HTML30'],.zooming,.main-container:before,.main-container:after,article .topic-tools,.article-ad,.Middle-Ad,.quote-share,.item-control.blog-admin,#TOC,.see-also{display:none}
.side-$startSide{width:100%!important;float:none}
.middle-content,.main-wrap{margin:0 auto!important}
.main-container{max-width:100%;-webkit-box-shadow:none;box-shadow:none}
.topic-details{margin:0 5%;width:90%}
.topic-title{color:#000;border-bottom:1px solid #DDD;background:none;-webkit-box-shadow:none;box-shadow:none;border-radius:0;text-shadow:none}
.post-body{color:#222!important}
.post-body pre.sq-code{max-height:100%}
}

/* Admin Custom CSS
===================*/

]]></b:skin>
    <b:if cond='data:view.isLayoutMode'>
      <!-- Layout Skin -->
      <b:template-skin><![CDATA[
/*  Common 
* ============= */
body#layout{margin:0 auto;background:#efefef url(https://3.bp.blogspot.com/-7be7oo1TgG4/WmSZXInnR5I/AAAAAAAAACc/z04mSX3fHpIblUl88DdieGnJXce4oIlwwCLcBGAs/s1600/pattern.png);min-height:100%;direction:rtl;width:778px;padding:0 10px}
body#layout .rtl{direction:rtl}
body#layout .ltr{direction:ltr}
body#layout:before{content:'';margin-bottom:10px;background:url(https://1.bp.blogspot.com/-pyLEzk_SFug/WxdcTw7oqgI/AAAAAAAAAUk/FzFWBlqdDVkMpi0nUmGQGMc2G29MRg3RgCK4BGAYYCw/s1600/Header.png) no-repeat center center;display:block;height:200px}
body#layout .clear{height:1px;clear:both;display:block;width:100%}
body#layout .Loading{display:none}
body#layout .section .widget-content{border-radius:2px;min-height:50px}
body#layout .rtl .section .widget-content{background:linear-gradient(45deg,#2c165f,#922c2c);}
body#layout .ltr .section .widget-content{background:linear-gradient(45deg,#922c2c,#2c165f);}
body#layout .draggable-widget .widget-wrap3{background:none}
body#layout .section h4{display:none}
body#layout div.layout-title{color:#ddd;font-weight:700;font-size:13px}
body#layout .rtl div.layout-title{text-align:right;}
body#layout .ltr div.layout-title{text-align:left;}
body#layout div.layout-widget-description{color:#b77881;font-size:11px;line-height:16px}
body#layout .rtl div.layout-widget-description{text-align:right;}
body#layout .ltr div.layout-widget-description{text-align:left;}
body#layout div.layout-title,body#layout div.layout-widget-description{font-family:tahoma}
body#layout .main-container .section{margin:0;border:0;padding:0;background:0;font-size:0;height:auto}
body#layout div.section>div.add_widget{margin-top:0;padding:7px 15px;border:none;border-bottom:2px solid #c7c7c7}
body#layout div.section>div.add_widget:hover{border-bottom:2px solid #e87375}
body#layout .add-icon{background-color:#d8d8d8;border-radius:2px}
body#layout div.section>div.add_widget:hover .add-icon{background-color:#e87375}
body#layout div.section>div.widget{margin-top:0;margin-bottom:10px}
body#layout .section .widget a.editlink{border:0;padding:3px 15px;color:#b190bf!important;background:#562d67;text-decoration:none;border-radius:3px;height:20px;font:700 11px/18px Tahoma}
body#layout .rtl .section .widget a.editlink{right:auto;left:10px;}
body#layout .ltr .section .widget a.editlink{left:auto;right:10px;}
body#layout .section .widget a.editlink:hover{background:#922c2d;color:#dadada!important}
body#layout .visibility .editlink.icon{margin-top:15px}
body#layout .add_widget{border:1px dashed rgba(0,0,0,0.3);margin-bottom:5px;margin-top:0}
body#layout .add_widget:hover{border:1px dashed rgba(0,0,0,0.5)}
body#layout .section .add_widget a{color:#757575;font-weight:700;text-decoration:none!important}
body#layout.rtl .section .add_widget a{margin-right:40px;margin-left:0;}
body#layout.ltr .section .add_widget a{margin-left:40px;margin-right:0;}
body#layout div.widget-content{padding:10px 15px}
body#layout .draggable-widget div.widget-wrap2{background-color:#e87375}
body#layout .dr_active:before{content:'\افلت هُنا';font-size:30px;padding-top:25px;display:block;font-weight:700}
body#layout .dr_active{height:50px!important;background-color:transparent;border:1px dashed #5558ea;color:#5e1056;margin-bottom:30px;top:20px;border-radius:100px}
body#layout .widget.locked-widget:before{content:'\002638';font-size:14px;position:absolute;z-index:2;top:0;background-color:#562d67;width:17px;height:15px;color:#b190b6;line-height:1em;padding-top:5px;border-radius:0 0 20px 20px}
body#layout .rtl .widget.locked-widget:before{left:10px}
body#layout .ltr .widget.locked-widget:before{right:10px}
body#layout .widget .visibility .layout-widget-state{margin-top:12px;background-image:url(https://4.bp.blogspot.com/-4ewGLNY2bfg/WmSZTIyIIfI/AAAAAAAAABg/hkOX-BjuVVUjfRomeZxjQtyVzTSEKa_WgCLcBGAs/s1600/eyes.png);opacity:1!important}
body#layout .rtl .widget .visibility .layout-widget-state{float:right;}
body#layout .ltr .widget .visibility .layout-widget-state{float:left;}
.layout-widget-state.visible{background-position:center -1px!important}
.layout-widget-state.not-visible{background-position:center -23px!important}

/*  Heads Title 
* ============= */
body#layout header:before,body#layout .intro:before,body#layout .sided-sections:before,body#layout aside:before,body#layout #RecentPosts:before,body#layout #Auth-Sec:before,body#layout footer:before{content:'';display:block;height:45px;background:#271e3a url(https://3.bp.blogspot.com/-lBhkNA-C7fk/WmSZM9WycNI/AAAAAAAAAAg/DxeNRhwfx8IIV8gg3CkKqcISm07mgH7hQCLcBGAs/s1600/LyN.png) no-repeat;margin-bottom:10px;margin-top:20px}
body#layout header:before{background-position:center -9px}
body#layout .intro:before{background-position:center -78px}
body#layout .sided-sections:before{background-position:center -152px}
body#layout aside:before{background-position:center -228px}
body#layout #RecentPosts:before{background-position:center -308px}
body#layout #Auth-Sec:before{background-position:center -398px}
body#layout footer:before{background-position:center -486px}
body#layout .opt-before:before{display:none}

/*  Header 
* ============= */
body#layout #Tempnec{background-color:transparent;border:none;margin:0;padding:0;overflow:hidden}
body#layout #Tempnec .widget{display:none}
body#layout .widget#LinkList400,body#layout .widget#HTML400{display:block;width:49.5%}
body#layout .rtl .widget#LinkList400, body#layout .rtl .widget#HTML400{float:right}
body#layout .ltr .widget#LinkList400, body#layout .ltr .widget#HTML400{float:left}
body#layout .rtl .widget#HTML400{margin-left:1%}
body#layout .ltr .widget#HTML400{margin-right:1%}
body#layout #top-bar #HTML301{display:none}
body#layout header div.dropregion{display:none!important}
body#layout #top-bar .widget, body#layout #head-sec .widget{display:inline-block;width:49.5%;vertical-align:top}
body#layout #head-sec #Header1{display:block;width:100%}
body#layout .rtl .widget#HTML302{margin-left:1%}
body#layout .ltr .widget#HTML302{margin-right:1%}
body#layout .rtl #top-bar #LinkList301{margin-right:1%}
body#layout .ltr #top-bar #LinkList301{margin-left:1%}

/*  Main 
* ============= */
body#layout .sided-sections,body#layout .sided-sections{font-size:0}
body#layout .top-content .sided-sections .section,body#layout .bottom-content .sided-sections .section{display:inline-block;vertical-align:top;width:32.33333333333%}
body#layout #section2, body#layout #section5, body#layout #section8, body#layout #section17, body#layout #section20{margin-right:1.5%;margin-left:1.5%}
body#layout .middle-content .sided-sections .section{display:inline-block;vertical-align:top;width:49%}
body#layout .rtl .middle-content .sided-sections .section:first-of-type{margin-left:2%}
body#layout .ltr .middle-content .sided-sections .section:last-of-type{margin-left:2%}
body#layout .intro{margin-bottom:10px}
body#layout #section9{margin-bottom:10px}
body#layout .middle-content{margin-bottom:10px}
body#layout .Blog .widget-content{height:100px}
body#layout #ContactForm93{display:none}
body#layout div#RecentPosts .widget:last-of-type{margin-bottom:0}
body#layout .rtl main, body#layout .rtl aside{float:right;}
body#layout .ltr main, body#layout .ltr aside{float:left;}
body#layout .rtl main{width:60%;border-left:2px solid #d8d8d8;}
body#layout .ltr main{width:60%;border-right:2px solid #d8d8d8;}
body#layout .rtl main{padding-left:1%}
body#layout .ltr main{padding-right:1%}
body#layout aside{width:37.5%;}
body#layout .rtl aside{margin-right:1%}
body#layout .ltr aside{margin-left:1%}

/*  footer 
* ============= */
body#layout #footer-sections{font-size:0}
body#layout #footer-sections .section{width:24%;display:inline-block;vertical-align:top}
body#layout .rtl #footer-sections .section{margin-left:1.333%}
body#layout .ltr #footer-sections .section{margin-right:1.333%}
body#layout .rtl #footer-sections div#sec4{margin-left:0}
body#layout .ltr #footer-sections div#sec4{margin-right:0}
body#layout #HTML303{display:none}
]]></b:template-skin>  
    </b:if>

    <!-- Font Awesome -->
    <style type='text/css'>/*<![CDATA[*/
@font-face{font-family:'FontAwesome';font-display:swap;src:url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/fonts/fontawesome-webfont.eot?v=4.7.0');src:url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/fonts/fontawesome-webfont.eot?#iefix&v=4.7.0') format('embedded-opentype'),url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/fonts/fontawesome-webfont.woff2?v=4.7.0') format('woff2'),url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/fonts/fontawesome-webfont.woff?v=4.7.0') format('woff'),url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/fonts/fontawesome-webfont.ttf?v=4.7.0') format('truetype'),url('https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/fonts/fontawesome-webfont.svg?v=4.7.0#fontawesomeregular') format('svg');font-weight:normal;font-style:normal}.fa{display:inline-block;font:normal normal normal 14px/1 FontAwesome;font-size:inherit;text-rendering:auto;-webkit-font-smoothing:antialiased;-moz-osx-font-smoothing:grayscale}.fa-lg{font-size:1.33333333em;line-height:.75em;vertical-align:-15%}.fa-2x{font-size:2em}.fa-3x{font-size:3em}.fa-4x{font-size:4em}.fa-5x{font-size:5em}.fa-fw{width:1.28571429em;text-align:center}.fa-ul{padding-left:0;margin-left:2.14285714em;list-style-type:none}.fa-ul>li{position:relative}.fa-li{position:absolute;left:-2.14285714em;width:2.14285714em;top:.14285714em;text-align:center}.fa-li.fa-lg{left:-1.85714286em}.fa-border{padding:.2em .25em .15em;border:solid .08em #eee;border-radius:.1em}.fa-pull-left{float:left}.fa-pull-right{float:right}.fa.fa-pull-left{margin-right:.3em}.fa.fa-pull-right{margin-left:.3em}.pull-right{float:right}.pull-left{float:left}.fa.pull-left{margin-right:.3em}.fa.pull-right{margin-left:.3em}.fa-spin{-webkit-animation:fa-spin 2s infinite linear;animation:fa-spin 2s infinite linear}.fa-pulse{-webkit-animation:fa-spin 1s infinite steps(8);animation:fa-spin 1s infinite steps(8)}@-webkit-keyframes fa-spin{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(359deg);transform:rotate(359deg)}}@keyframes fa-spin{0%{-webkit-transform:rotate(0deg);transform:rotate(0deg)}100%{-webkit-transform:rotate(359deg);transform:rotate(359deg)}}.fa-rotate-90{-ms-filter:"progid:DXImageTransform.Microsoft.BasicImage(rotation=1)";-webkit-transform:rotate(90deg);-ms-transform:rotate(90deg);transform:rotate(90deg)}.fa-rotate-180{-ms-filter:"progid:DXImageTransform.Microsoft.BasicImage(rotation=2)";-webkit-transform:rotate(180deg);-ms-transform:rotate(180deg);transform:rotate(180deg)}.fa-rotate-270{-ms-filter:"progid:DXImageTransform.Microsoft.BasicImage(rotation=3)";-webkit-transform:rotate(270deg);-ms-transform:rotate(270deg);transform:rotate(270deg)}.fa-flip-horizontal{-ms-filter:"progid:DXImageTransform.Microsoft.BasicImage(rotation=0, mirror=1)";-webkit-transform:scale(-1, 1);-ms-transform:scale(-1, 1);transform:scale(-1, 1)}.fa-flip-vertical{-ms-filter:"progid:DXImageTransform.Microsoft.BasicImage(rotation=2, mirror=1)";-webkit-transform:scale(1, -1);-ms-transform:scale(1, -1);transform:scale(1, -1)}:root .fa-rotate-90,:root .fa-rotate-180,:root .fa-rotate-270,:root .fa-flip-horizontal,:root .fa-flip-vertical{filter:none}.fa-stack{position:relative;display:inline-block;width:2em;height:2em;line-height:2em;vertical-align:middle}.fa-stack-1x,.fa-stack-2x{position:absolute;left:0;width:100%;text-align:center}.fa-stack-1x{line-height:inherit}.fa-stack-2x{font-size:2em}.fa-inverse{color:#fff}.fa-glass:before{content:"\f000"}.fa-music:before{content:"\f001"}.fa-search:before{content:"\f002"}.fa-envelope-o:before{content:"\f003"}.fa-heart:before{content:"\f004"}.fa-star:before{content:"\f005"}.fa-star-o:before{content:"\f006"}.fa-user:before{content:"\f007"}.fa-film:before{content:"\f008"}.fa-th-large:before{content:"\f009"}.fa-th:before{content:"\f00a"}.fa-th-list:before{content:"\f00b"}.fa-check:before{content:"\f00c"}.fa-remove:before,.fa-close:before,.fa-times:before{content:"\f00d"}.fa-search-plus:before{content:"\f00e"}.fa-search-minus:before{content:"\f010"}.fa-power-off:before{content:"\f011"}.fa-signal:before{content:"\f012"}.fa-gear:before,.fa-cog:before{content:"\f013"}.fa-trash-o:before{content:"\f014"}.fa-home:before{content:"\f015"}.fa-file-o:before{content:"\f016"}.fa-clock-o:before{content:"\f017"}.fa-road:before{content:"\f018"}.fa-download:before{content:"\f019"}.fa-arrow-circle-o-down:before{content:"\f01a"}.fa-arrow-circle-o-up:before{content:"\f01b"}.fa-inbox:before{content:"\f01c"}.fa-play-circle-o:before{content:"\f01d"}.fa-rotate-right:before,.fa-repeat:before{content:"\f01e"}.fa-refresh:before{content:"\f021"}.fa-list-alt:before{content:"\f022"}.fa-lock:before{content:"\f023"}.fa-flag:before{content:"\f024"}.fa-headphones:before{content:"\f025"}.fa-volume-off:before{content:"\f026"}.fa-volume-down:before{content:"\f027"}.fa-volume-up:before{content:"\f028"}.fa-qrcode:before{content:"\f029"}.fa-barcode:before{content:"\f02a"}.fa-tag:before{content:"\f02b"}.fa-tags:before{content:"\f02c"}.fa-book:before{content:"\f02d"}.fa-bookmark:before{content:"\f02e"}.fa-print:before{content:"\f02f"}.fa-camera:before{content:"\f030"}.fa-font:before{content:"\f031"}.fa-bold:before{content:"\f032"}.fa-italic:before{content:"\f033"}.fa-text-height:before{content:"\f034"}.fa-text-width:before{content:"\f035"}.fa-align-left:before{content:"\f036"}.fa-align-center:before{content:"\f037"}.fa-align-right:before{content:"\f038"}.fa-align-justify:before{content:"\f039"}.fa-list:before{content:"\f03a"}.fa-dedent:before,.fa-outdent:before{content:"\f03b"}.fa-indent:before{content:"\f03c"}.fa-video-camera:before{content:"\f03d"}.fa-photo:before,.fa-image:before,.fa-picture-o:before{content:"\f03e"}.fa-pencil:before{content:"\f040"}.fa-map-marker:before{content:"\f041"}.fa-adjust:before{content:"\f042"}.fa-tint:before{content:"\f043"}.fa-edit:before,.fa-pencil-square-o:before{content:"\f044"}.fa-share-square-o:before{content:"\f045"}.fa-check-square-o:before{content:"\f046"}.fa-arrows:before{content:"\f047"}.fa-step-backward:before{content:"\f048"}.fa-fast-backward:before{content:"\f049"}.fa-backward:before{content:"\f04a"}.fa-play:before{content:"\f04b"}.fa-pause:before{content:"\f04c"}.fa-stop:before{content:"\f04d"}.fa-forward:before{content:"\f04e"}.fa-fast-forward:before{content:"\f050"}.fa-step-forward:before{content:"\f051"}.fa-eject:before{content:"\f052"}.fa-chevron-left:before{content:"\f053"}.fa-chevron-right:before{content:"\f054"}.fa-plus-circle:before{content:"\f055"}.fa-minus-circle:before{content:"\f056"}.fa-times-circle:before{content:"\f057"}.fa-check-circle:before{content:"\f058"}.fa-question-circle:before{content:"\f059"}.fa-info-circle:before{content:"\f05a"}.fa-crosshairs:before{content:"\f05b"}.fa-times-circle-o:before{content:"\f05c"}.fa-check-circle-o:before{content:"\f05d"}.fa-ban:before{content:"\f05e"}.fa-arrow-left:before{content:"\f060"}.fa-arrow-right:before{content:"\f061"}.fa-arrow-up:before{content:"\f062"}.fa-arrow-down:before{content:"\f063"}.fa-mail-forward:before,.fa-share:before{content:"\f064"}.fa-expand:before{content:"\f065"}.fa-compress:before{content:"\f066"}.fa-plus:before{content:"\f067"}.fa-minus:before{content:"\f068"}.fa-asterisk:before{content:"\f069"}.fa-exclamation-circle:before{content:"\f06a"}.fa-gift:before{content:"\f06b"}.fa-leaf:before{content:"\f06c"}.fa-fire:before{content:"\f06d"}.fa-eye:before{content:"\f06e"}.fa-eye-slash:before{content:"\f070"}.fa-warning:before,.fa-exclamation-triangle:before{content:"\f071"}.fa-plane:before{content:"\f072"}.fa-calendar:before{content:"\f073"}.fa-random:before{content:"\f074"}.fa-comment:before{content:"\f075"}.fa-magnet:before{content:"\f076"}.fa-chevron-up:before{content:"\f077"}.fa-chevron-down:before{content:"\f078"}.fa-retweet:before{content:"\f079"}.fa-shopping-cart:before{content:"\f07a"}.fa-folder:before{content:"\f07b"}.fa-folder-open:before{content:"\f07c"}.fa-arrows-v:before{content:"\f07d"}.fa-arrows-h:before{content:"\f07e"}.fa-bar-chart-o:before,.fa-bar-chart:before{content:"\f080"}.fa-twitter-square:before{content:"\f081"}.fa-facebook-square:before{content:"\f082"}.fa-camera-retro:before{content:"\f083"}.fa-key:before{content:"\f084"}.fa-gears:before,.fa-cogs:before{content:"\f085"}.fa-comments:before{content:"\f086"}.fa-thumbs-o-up:before{content:"\f087"}.fa-thumbs-o-down:before{content:"\f088"}.fa-star-half:before{content:"\f089"}.fa-heart-o:before{content:"\f08a"}.fa-sign-out:before{content:"\f08b"}.fa-linkedin-square:before{content:"\f08c"}.fa-thumb-tack:before{content:"\f08d"}.fa-external-link:before{content:"\f08e"}.fa-sign-in:before{content:"\f090"}.fa-trophy:before{content:"\f091"}.fa-github-square:before{content:"\f092"}.fa-upload:before{content:"\f093"}.fa-lemon-o:before{content:"\f094"}.fa-phone:before{content:"\f095"}.fa-square-o:before{content:"\f096"}.fa-bookmark-o:before{content:"\f097"}.fa-phone-square:before{content:"\f098"}.fa-twitter:before{content:"\f099"}.fa-facebook-f:before,.fa-facebook:before{content:"\f09a"}.fa-github:before{content:"\f09b"}.fa-unlock:before{content:"\f09c"}.fa-credit-card:before{content:"\f09d"}.fa-feed:before,.fa-rss:before{content:"\f09e"}.fa-hdd-o:before{content:"\f0a0"}.fa-bullhorn:before{content:"\f0a1"}.fa-bell:before{content:"\f0f3"}.fa-certificate:before{content:"\f0a3"}.fa-hand-o-right:before{content:"\f0a4"}.fa-hand-o-left:before{content:"\f0a5"}.fa-hand-o-up:before{content:"\f0a6"}.fa-hand-o-down:before{content:"\f0a7"}.fa-arrow-circle-left:before{content:"\f0a8"}.fa-arrow-circle-right:before{content:"\f0a9"}.fa-arrow-circle-up:before{content:"\f0aa"}.fa-arrow-circle-down:before{content:"\f0ab"}.fa-globe:before{content:"\f0ac"}.fa-wrench:before{content:"\f0ad"}.fa-tasks:before{content:"\f0ae"}.fa-filter:before{content:"\f0b0"}.fa-briefcase:before{content:"\f0b1"}.fa-arrows-alt:before{content:"\f0b2"}.fa-group:before,.fa-users:before{content:"\f0c0"}.fa-chain:before,.fa-link:before{content:"\f0c1"}.fa-cloud:before{content:"\f0c2"}.fa-flask:before{content:"\f0c3"}.fa-cut:before,.fa-scissors:before{content:"\f0c4"}.fa-copy:before,.fa-files-o:before{content:"\f0c5"}.fa-paperclip:before{content:"\f0c6"}.fa-save:before,.fa-floppy-o:before{content:"\f0c7"}.fa-square:before{content:"\f0c8"}.fa-navicon:before,.fa-reorder:before,.fa-bars:before{content:"\f0c9"}.fa-list-ul:before{content:"\f0ca"}.fa-list-ol:before{content:"\f0cb"}.fa-strikethrough:before{content:"\f0cc"}.fa-underline:before{content:"\f0cd"}.fa-table:before{content:"\f0ce"}.fa-magic:before{content:"\f0d0"}.fa-truck:before{content:"\f0d1"}.fa-pinterest:before{content:"\f0d2"}.fa-pinterest-square:before{content:"\f0d3"}.fa-google-plus-square:before{content:"\f0d4"}.fa-google-plus:before{content:"\f0d5"}.fa-money:before{content:"\f0d6"}.fa-caret-down:before{content:"\f0d7"}.fa-caret-up:before{content:"\f0d8"}.fa-caret-left:before{content:"\f0d9"}.fa-caret-right:before{content:"\f0da"}.fa-columns:before{content:"\f0db"}.fa-unsorted:before,.fa-sort:before{content:"\f0dc"}.fa-sort-down:before,.fa-sort-desc:before{content:"\f0dd"}.fa-sort-up:before,.fa-sort-asc:before{content:"\f0de"}.fa-envelope:before{content:"\f0e0"}.fa-linkedin:before{content:"\f0e1"}.fa-rotate-left:before,.fa-undo:before{content:"\f0e2"}.fa-legal:before,.fa-gavel:before{content:"\f0e3"}.fa-dashboard:before,.fa-tachometer:before{content:"\f0e4"}.fa-comment-o:before{content:"\f0e5"}.fa-comments-o:before{content:"\f0e6"}.fa-flash:before,.fa-bolt:before{content:"\f0e7"}.fa-sitemap:before{content:"\f0e8"}.fa-umbrella:before{content:"\f0e9"}.fa-paste:before,.fa-clipboard:before{content:"\f0ea"}.fa-lightbulb-o:before{content:"\f0eb"}.fa-exchange:before{content:"\f0ec"}.fa-cloud-download:before{content:"\f0ed"}.fa-cloud-upload:before{content:"\f0ee"}.fa-user-md:before{content:"\f0f0"}.fa-stethoscope:before{content:"\f0f1"}.fa-suitcase:before{content:"\f0f2"}.fa-bell-o:before{content:"\f0a2"}.fa-coffee:before{content:"\f0f4"}.fa-cutlery:before{content:"\f0f5"}.fa-file-text-o:before{content:"\f0f6"}.fa-building-o:before{content:"\f0f7"}.fa-hospital-o:before{content:"\f0f8"}.fa-ambulance:before{content:"\f0f9"}.fa-medkit:before{content:"\f0fa"}.fa-fighter-jet:before{content:"\f0fb"}.fa-beer:before{content:"\f0fc"}.fa-h-square:before{content:"\f0fd"}.fa-plus-square:before{content:"\f0fe"}.fa-angle-double-left:before{content:"\f100"}.fa-angle-double-right:before{content:"\f101"}.fa-angle-double-up:before{content:"\f102"}.fa-angle-double-down:before{content:"\f103"}.fa-angle-left:before{content:"\f104"}.fa-angle-right:before{content:"\f105"}.fa-angle-up:before{content:"\f106"}.fa-angle-down:before{content:"\f107"}.fa-desktop:before{content:"\f108"}.fa-laptop:before{content:"\f109"}.fa-tablet:before{content:"\f10a"}.fa-mobile-phone:before,.fa-mobile:before{content:"\f10b"}.fa-circle-o:before{content:"\f10c"}.fa-quote-left:before{content:"\f10d"}.fa-quote-right:before{content:"\f10e"}.fa-spinner:before{content:"\f110"}.fa-circle:before{content:"\f111"}.fa-mail-reply:before,.fa-reply:before{content:"\f112"}.fa-github-alt:before{content:"\f113"}.fa-folder-o:before{content:"\f114"}.fa-folder-open-o:before{content:"\f115"}.fa-smile-o:before{content:"\f118"}.fa-frown-o:before{content:"\f119"}.fa-meh-o:before{content:"\f11a"}.fa-gamepad:before{content:"\f11b"}.fa-keyboard-o:before{content:"\f11c"}.fa-flag-o:before{content:"\f11d"}.fa-flag-checkered:before{content:"\f11e"}.fa-terminal:before{content:"\f120"}.fa-code:before{content:"\f121"}.fa-mail-reply-all:before,.fa-reply-all:before{content:"\f122"}.fa-star-half-empty:before,.fa-star-half-full:before,.fa-star-half-o:before{content:"\f123"}.fa-location-arrow:before{content:"\f124"}.fa-crop:before{content:"\f125"}.fa-code-fork:before{content:"\f126"}.fa-unlink:before,.fa-chain-broken:before{content:"\f127"}.fa-question:before{content:"\f128"}.fa-info:before{content:"\f129"}.fa-exclamation:before{content:"\f12a"}.fa-superscript:before{content:"\f12b"}.fa-subscript:before{content:"\f12c"}.fa-eraser:before{content:"\f12d"}.fa-puzzle-piece:before{content:"\f12e"}.fa-microphone:before{content:"\f130"}.fa-microphone-slash:before{content:"\f131"}.fa-shield:before{content:"\f132"}.fa-calendar-o:before{content:"\f133"}.fa-fire-extinguisher:before{content:"\f134"}.fa-rocket:before{content:"\f135"}.fa-maxcdn:before{content:"\f136"}.fa-chevron-circle-left:before{content:"\f137"}.fa-chevron-circle-right:before{content:"\f138"}.fa-chevron-circle-up:before{content:"\f139"}.fa-chevron-circle-down:before{content:"\f13a"}.fa-html5:before{content:"\f13b"}.fa-css3:before{content:"\f13c"}.fa-anchor:before{content:"\f13d"}.fa-unlock-alt:before{content:"\f13e"}.fa-bullseye:before{content:"\f140"}.fa-ellipsis-h:before{content:"\f141"}.fa-ellipsis-v:before{content:"\f142"}.fa-rss-square:before{content:"\f143"}.fa-play-circle:before{content:"\f144"}.fa-ticket:before{content:"\f145"}.fa-minus-square:before{content:"\f146"}.fa-minus-square-o:before{content:"\f147"}.fa-level-up:before{content:"\f148"}.fa-level-down:before{content:"\f149"}.fa-check-square:before{content:"\f14a"}.fa-pencil-square:before{content:"\f14b"}.fa-external-link-square:before{content:"\f14c"}.fa-share-square:before{content:"\f14d"}.fa-compass:before{content:"\f14e"}.fa-toggle-down:before,.fa-caret-square-o-down:before{content:"\f150"}.fa-toggle-up:before,.fa-caret-square-o-up:before{content:"\f151"}.fa-toggle-right:before,.fa-caret-square-o-right:before{content:"\f152"}.fa-euro:before,.fa-eur:before{content:"\f153"}.fa-gbp:before{content:"\f154"}.fa-dollar:before,.fa-usd:before{content:"\f155"}.fa-rupee:before,.fa-inr:before{content:"\f156"}.fa-cny:before,.fa-rmb:before,.fa-yen:before,.fa-jpy:before{content:"\f157"}.fa-ruble:before,.fa-rouble:before,.fa-rub:before{content:"\f158"}.fa-won:before,.fa-krw:before{content:"\f159"}.fa-bitcoin:before,.fa-btc:before{content:"\f15a"}.fa-file:before{content:"\f15b"}.fa-file-text:before{content:"\f15c"}.fa-sort-alpha-asc:before{content:"\f15d"}.fa-sort-alpha-desc:before{content:"\f15e"}.fa-sort-amount-asc:before{content:"\f160"}.fa-sort-amount-desc:before{content:"\f161"}.fa-sort-numeric-asc:before{content:"\f162"}.fa-sort-numeric-desc:before{content:"\f163"}.fa-thumbs-up:before{content:"\f164"}.fa-thumbs-down:before{content:"\f165"}.fa-youtube-square:before{content:"\f166"}.fa-youtube:before{content:"\f167"}.fa-xing:before{content:"\f168"}.fa-xing-square:before{content:"\f169"}.fa-youtube-play:before{content:"\f16a"}.fa-dropbox:before{content:"\f16b"}.fa-stack-overflow:before{content:"\f16c"}.fa-instagram:before{content:"\f16d"}.fa-flickr:before{content:"\f16e"}.fa-adn:before{content:"\f170"}.fa-bitbucket:before{content:"\f171"}.fa-bitbucket-square:before{content:"\f172"}.fa-tumblr:before{content:"\f173"}.fa-tumblr-square:before{content:"\f174"}.fa-long-arrow-down:before{content:"\f175"}.fa-long-arrow-up:before{content:"\f176"}.fa-long-arrow-left:before{content:"\f177"}.fa-long-arrow-right:before{content:"\f178"}.fa-apple:before{content:"\f179"}.fa-windows:before{content:"\f17a"}.fa-android:before{content:"\f17b"}.fa-linux:before{content:"\f17c"}.fa-dribbble:before{content:"\f17d"}.fa-skype:before{content:"\f17e"}.fa-foursquare:before{content:"\f180"}.fa-trello:before{content:"\f181"}.fa-female:before{content:"\f182"}.fa-male:before{content:"\f183"}.fa-gittip:before,.fa-gratipay:before{content:"\f184"}.fa-sun-o:before{content:"\f185"}.fa-moon-o:before{content:"\f186"}.fa-archive:before{content:"\f187"}.fa-bug:before{content:"\f188"}.fa-vk:before{content:"\f189"}.fa-weibo:before{content:"\f18a"}.fa-renren:before{content:"\f18b"}.fa-pagelines:before{content:"\f18c"}.fa-stack-exchange:before{content:"\f18d"}.fa-arrow-circle-o-right:before{content:"\f18e"}.fa-arrow-circle-o-left:before{content:"\f190"}.fa-toggle-left:before,.fa-caret-square-o-left:before{content:"\f191"}.fa-dot-circle-o:before{content:"\f192"}.fa-wheelchair:before{content:"\f193"}.fa-vimeo-square:before{content:"\f194"}.fa-turkish-lira:before,.fa-try:before{content:"\f195"}.fa-plus-square-o:before{content:"\f196"}.fa-space-shuttle:before{content:"\f197"}.fa-slack:before{content:"\f198"}.fa-envelope-square:before{content:"\f199"}.fa-wordpress:before{content:"\f19a"}.fa-openid:before{content:"\f19b"}.fa-institution:before,.fa-bank:before,.fa-university:before{content:"\f19c"}.fa-mortar-board:before,.fa-graduation-cap:before{content:"\f19d"}.fa-yahoo:before{content:"\f19e"}.fa-google:before{content:"\f1a0"}.fa-reddit:before{content:"\f1a1"}.fa-reddit-square:before{content:"\f1a2"}.fa-stumbleupon-circle:before{content:"\f1a3"}.fa-stumbleupon:before{content:"\f1a4"}.fa-delicious:before{content:"\f1a5"}.fa-digg:before{content:"\f1a6"}.fa-pied-piper-pp:before{content:"\f1a7"}.fa-pied-piper-alt:before{content:"\f1a8"}.fa-drupal:before{content:"\f1a9"}.fa-joomla:before{content:"\f1aa"}.fa-language:before{content:"\f1ab"}.fa-fax:before{content:"\f1ac"}.fa-building:before{content:"\f1ad"}.fa-child:before{content:"\f1ae"}.fa-paw:before{content:"\f1b0"}.fa-spoon:before{content:"\f1b1"}.fa-cube:before{content:"\f1b2"}.fa-cubes:before{content:"\f1b3"}.fa-behance:before{content:"\f1b4"}.fa-behance-square:before{content:"\f1b5"}.fa-steam:before{content:"\f1b6"}.fa-steam-square:before{content:"\f1b7"}.fa-recycle:before{content:"\f1b8"}.fa-automobile:before,.fa-car:before{content:"\f1b9"}.fa-cab:before,.fa-taxi:before{content:"\f1ba"}.fa-tree:before{content:"\f1bb"}.fa-spotify:before{content:"\f1bc"}.fa-deviantart:before{content:"\f1bd"}.fa-soundcloud:before{content:"\f1be"}.fa-database:before{content:"\f1c0"}.fa-file-pdf-o:before{content:"\f1c1"}.fa-file-word-o:before{content:"\f1c2"}.fa-file-excel-o:before{content:"\f1c3"}.fa-file-powerpoint-o:before{content:"\f1c4"}.fa-file-photo-o:before,.fa-file-picture-o:before,.fa-file-image-o:before{content:"\f1c5"}.fa-file-zip-o:before,.fa-file-archive-o:before{content:"\f1c6"}.fa-file-sound-o:before,.fa-file-audio-o:before{content:"\f1c7"}.fa-file-movie-o:before,.fa-file-video-o:before{content:"\f1c8"}.fa-file-code-o:before{content:"\f1c9"}.fa-vine:before{content:"\f1ca"}.fa-codepen:before{content:"\f1cb"}.fa-jsfiddle:before{content:"\f1cc"}.fa-life-bouy:before,.fa-life-buoy:before,.fa-life-saver:before,.fa-support:before,.fa-life-ring:before{content:"\f1cd"}.fa-circle-o-notch:before{content:"\f1ce"}.fa-ra:before,.fa-resistance:before,.fa-rebel:before{content:"\f1d0"}.fa-ge:before,.fa-empire:before{content:"\f1d1"}.fa-git-square:before{content:"\f1d2"}.fa-git:before{content:"\f1d3"}.fa-y-combinator-square:before,.fa-yc-square:before,.fa-hacker-news:before{content:"\f1d4"}.fa-tencent-weibo:before{content:"\f1d5"}.fa-qq:before{content:"\f1d6"}.fa-wechat:before,.fa-weixin:before{content:"\f1d7"}.fa-send:before,.fa-paper-plane:before{content:"\f1d8"}.fa-send-o:before,.fa-paper-plane-o:before{content:"\f1d9"}.fa-history:before{content:"\f1da"}.fa-circle-thin:before{content:"\f1db"}.fa-header:before{content:"\f1dc"}.fa-paragraph:before{content:"\f1dd"}.fa-sliders:before{content:"\f1de"}.fa-share-alt:before{content:"\f1e0"}.fa-share-alt-square:before{content:"\f1e1"}.fa-bomb:before{content:"\f1e2"}.fa-soccer-ball-o:before,.fa-futbol-o:before{content:"\f1e3"}.fa-tty:before{content:"\f1e4"}.fa-binoculars:before{content:"\f1e5"}.fa-plug:before{content:"\f1e6"}.fa-slideshare:before{content:"\f1e7"}.fa-twitch:before{content:"\f1e8"}.fa-yelp:before{content:"\f1e9"}.fa-newspaper-o:before{content:"\f1ea"}.fa-wifi:before{content:"\f1eb"}.fa-calculator:before{content:"\f1ec"}.fa-paypal:before{content:"\f1ed"}.fa-google-wallet:before{content:"\f1ee"}.fa-cc-visa:before{content:"\f1f0"}.fa-cc-mastercard:before{content:"\f1f1"}.fa-cc-discover:before{content:"\f1f2"}.fa-cc-amex:before{content:"\f1f3"}.fa-cc-paypal:before{content:"\f1f4"}.fa-cc-stripe:before{content:"\f1f5"}.fa-bell-slash:before{content:"\f1f6"}.fa-bell-slash-o:before{content:"\f1f7"}.fa-trash:before{content:"\f1f8"}.fa-copyright:before{content:"\f1f9"}.fa-at:before{content:"\f1fa"}.fa-eyedropper:before{content:"\f1fb"}.fa-paint-brush:before{content:"\f1fc"}.fa-birthday-cake:before{content:"\f1fd"}.fa-area-chart:before{content:"\f1fe"}.fa-pie-chart:before{content:"\f200"}.fa-line-chart:before{content:"\f201"}.fa-lastfm:before{content:"\f202"}.fa-lastfm-square:before{content:"\f203"}.fa-toggle-off:before{content:"\f204"}.fa-toggle-on:before{content:"\f205"}.fa-bicycle:before{content:"\f206"}.fa-bus:before{content:"\f207"}.fa-ioxhost:before{content:"\f208"}.fa-angellist:before{content:"\f209"}.fa-cc:before{content:"\f20a"}.fa-shekel:before,.fa-sheqel:before,.fa-ils:before{content:"\f20b"}.fa-meanpath:before{content:"\f20c"}.fa-buysellads:before{content:"\f20d"}.fa-connectdevelop:before{content:"\f20e"}.fa-dashcube:before{content:"\f210"}.fa-forumbee:before{content:"\f211"}.fa-leanpub:before{content:"\f212"}.fa-sellsy:before{content:"\f213"}.fa-shirtsinbulk:before{content:"\f214"}.fa-simplybuilt:before{content:"\f215"}.fa-skyatlas:before{content:"\f216"}.fa-cart-plus:before{content:"\f217"}.fa-cart-arrow-down:before{content:"\f218"}.fa-diamond:before{content:"\f219"}.fa-ship:before{content:"\f21a"}.fa-user-secret:before{content:"\f21b"}.fa-motorcycle:before{content:"\f21c"}.fa-street-view:before{content:"\f21d"}.fa-heartbeat:before{content:"\f21e"}.fa-venus:before{content:"\f221"}.fa-mars:before{content:"\f222"}.fa-mercury:before{content:"\f223"}.fa-intersex:before,.fa-transgender:before{content:"\f224"}.fa-transgender-alt:before{content:"\f225"}.fa-venus-double:before{content:"\f226"}.fa-mars-double:before{content:"\f227"}.fa-venus-mars:before{content:"\f228"}.fa-mars-stroke:before{content:"\f229"}.fa-mars-stroke-v:before{content:"\f22a"}.fa-mars-stroke-h:before{content:"\f22b"}.fa-neuter:before{content:"\f22c"}.fa-genderless:before{content:"\f22d"}.fa-facebook-official:before{content:"\f230"}.fa-pinterest-p:before{content:"\f231"}.fa-whatsapp:before{content:"\f232"}.fa-server:before{content:"\f233"}.fa-user-plus:before{content:"\f234"}.fa-user-times:before{content:"\f235"}.fa-hotel:before,.fa-bed:before{content:"\f236"}.fa-viacoin:before{content:"\f237"}.fa-train:before{content:"\f238"}.fa-subway:before{content:"\f239"}.fa-medium:before{content:"\f23a"}.fa-yc:before,.fa-y-combinator:before{content:"\f23b"}.fa-optin-monster:before{content:"\f23c"}.fa-opencart:before{content:"\f23d"}.fa-expeditedssl:before{content:"\f23e"}.fa-battery-4:before,.fa-battery:before,.fa-battery-full:before{content:"\f240"}.fa-battery-3:before,.fa-battery-three-quarters:before{content:"\f241"}.fa-battery-2:before,.fa-battery-half:before{content:"\f242"}.fa-battery-1:before,.fa-battery-quarter:before{content:"\f243"}.fa-battery-0:before,.fa-battery-empty:before{content:"\f244"}.fa-mouse-pointer:before{content:"\f245"}.fa-i-cursor:before{content:"\f246"}.fa-object-group:before{content:"\f247"}.fa-object-ungroup:before{content:"\f248"}.fa-sticky-note:before{content:"\f249"}.fa-sticky-note-o:before{content:"\f24a"}.fa-cc-jcb:before{content:"\f24b"}.fa-cc-diners-club:before{content:"\f24c"}.fa-clone:before{content:"\f24d"}.fa-balance-scale:before{content:"\f24e"}.fa-hourglass-o:before{content:"\f250"}.fa-hourglass-1:before,.fa-hourglass-start:before{content:"\f251"}.fa-hourglass-2:before,.fa-hourglass-half:before{content:"\f252"}.fa-hourglass-3:before,.fa-hourglass-end:before{content:"\f253"}.fa-hourglass:before{content:"\f254"}.fa-hand-grab-o:before,.fa-hand-rock-o:before{content:"\f255"}.fa-hand-stop-o:before,.fa-hand-paper-o:before{content:"\f256"}.fa-hand-scissors-o:before{content:"\f257"}.fa-hand-lizard-o:before{content:"\f258"}.fa-hand-spock-o:before{content:"\f259"}.fa-hand-pointer-o:before{content:"\f25a"}.fa-hand-peace-o:before{content:"\f25b"}.fa-trademark:before{content:"\f25c"}.fa-registered:before{content:"\f25d"}.fa-creative-commons:before{content:"\f25e"}.fa-gg:before{content:"\f260"}.fa-gg-circle:before{content:"\f261"}.fa-tripadvisor:before{content:"\f262"}.fa-odnoklassniki:before{content:"\f263"}.fa-odnoklassniki-square:before{content:"\f264"}.fa-get-pocket:before{content:"\f265"}.fa-wikipedia-w:before{content:"\f266"}.fa-safari:before{content:"\f267"}.fa-chrome:before{content:"\f268"}.fa-firefox:before{content:"\f269"}.fa-opera:before{content:"\f26a"}.fa-internet-explorer:before{content:"\f26b"}.fa-tv:before,.fa-television:before{content:"\f26c"}.fa-contao:before{content:"\f26d"}.fa-500px:before{content:"\f26e"}.fa-amazon:before{content:"\f270"}.fa-calendar-plus-o:before{content:"\f271"}.fa-calendar-minus-o:before{content:"\f272"}.fa-calendar-times-o:before{content:"\f273"}.fa-calendar-check-o:before{content:"\f274"}.fa-industry:before{content:"\f275"}.fa-map-pin:before{content:"\f276"}.fa-map-signs:before{content:"\f277"}.fa-map-o:before{content:"\f278"}.fa-map:before{content:"\f279"}.fa-commenting:before{content:"\f27a"}.fa-commenting-o:before{content:"\f27b"}.fa-houzz:before{content:"\f27c"}.fa-vimeo:before{content:"\f27d"}.fa-black-tie:before{content:"\f27e"}.fa-fonticons:before{content:"\f280"}.fa-reddit-alien:before{content:"\f281"}.fa-edge:before{content:"\f282"}.fa-credit-card-alt:before{content:"\f283"}.fa-codiepie:before{content:"\f284"}.fa-modx:before{content:"\f285"}.fa-fort-awesome:before{content:"\f286"}.fa-usb:before{content:"\f287"}.fa-product-hunt:before{content:"\f288"}.fa-mixcloud:before{content:"\f289"}.fa-scribd:before{content:"\f28a"}.fa-pause-circle:before{content:"\f28b"}.fa-pause-circle-o:before{content:"\f28c"}.fa-stop-circle:before{content:"\f28d"}.fa-stop-circle-o:before{content:"\f28e"}.fa-shopping-bag:before{content:"\f290"}.fa-shopping-basket:before{content:"\f291"}.fa-hashtag:before{content:"\f292"}.fa-bluetooth:before{content:"\f293"}.fa-bluetooth-b:before{content:"\f294"}.fa-percent:before{content:"\f295"}.fa-gitlab:before{content:"\f296"}.fa-wpbeginner:before{content:"\f297"}.fa-wpforms:before{content:"\f298"}.fa-envira:before{content:"\f299"}.fa-universal-access:before{content:"\f29a"}.fa-wheelchair-alt:before{content:"\f29b"}.fa-question-circle-o:before{content:"\f29c"}.fa-blind:before{content:"\f29d"}.fa-audio-description:before{content:"\f29e"}.fa-volume-control-phone:before{content:"\f2a0"}.fa-braille:before{content:"\f2a1"}.fa-assistive-listening-systems:before{content:"\f2a2"}.fa-asl-interpreting:before,.fa-american-sign-language-interpreting:before{content:"\f2a3"}.fa-deafness:before,.fa-hard-of-hearing:before,.fa-deaf:before{content:"\f2a4"}.fa-glide:before{content:"\f2a5"}.fa-glide-g:before{content:"\f2a6"}.fa-signing:before,.fa-sign-language:before{content:"\f2a7"}.fa-low-vision:before{content:"\f2a8"}.fa-viadeo:before{content:"\f2a9"}.fa-viadeo-square:before{content:"\f2aa"}.fa-snapchat:before{content:"\f2ab"}.fa-snapchat-ghost:before{content:"\f2ac"}.fa-snapchat-square:before{content:"\f2ad"}.fa-pied-piper:before{content:"\f2ae"}.fa-first-order:before{content:"\f2b0"}.fa-yoast:before{content:"\f2b1"}.fa-themeisle:before{content:"\f2b2"}.fa-google-plus-circle:before,.fa-google-plus-official:before{content:"\f2b3"}.fa-fa:before,.fa-font-awesome:before{content:"\f2b4"}.fa-handshake-o:before{content:"\f2b5"}.fa-envelope-open:before{content:"\f2b6"}.fa-envelope-open-o:before{content:"\f2b7"}.fa-linode:before{content:"\f2b8"}.fa-address-book:before{content:"\f2b9"}.fa-address-book-o:before{content:"\f2ba"}.fa-vcard:before,.fa-address-card:before{content:"\f2bb"}.fa-vcard-o:before,.fa-address-card-o:before{content:"\f2bc"}.fa-user-circle:before{content:"\f2bd"}.fa-user-circle-o:before{content:"\f2be"}.fa-user-o:before{content:"\f2c0"}.fa-id-badge:before{content:"\f2c1"}.fa-drivers-license:before,.fa-id-card:before{content:"\f2c2"}.fa-drivers-license-o:before,.fa-id-card-o:before{content:"\f2c3"}.fa-quora:before{content:"\f2c4"}.fa-free-code-camp:before{content:"\f2c5"}.fa-telegram:before{content:"\f2c6"}.fa-thermometer-4:before,.fa-thermometer:before,.fa-thermometer-full:before{content:"\f2c7"}.fa-thermometer-3:before,.fa-thermometer-three-quarters:before{content:"\f2c8"}.fa-thermometer-2:before,.fa-thermometer-half:before{content:"\f2c9"}.fa-thermometer-1:before,.fa-thermometer-quarter:before{content:"\f2ca"}.fa-thermometer-0:before,.fa-thermometer-empty:before{content:"\f2cb"}.fa-shower:before{content:"\f2cc"}.fa-bathtub:before,.fa-s15:before,.fa-bath:before{content:"\f2cd"}.fa-podcast:before{content:"\f2ce"}.fa-window-maximize:before{content:"\f2d0"}.fa-window-minimize:before{content:"\f2d1"}.fa-window-restore:before{content:"\f2d2"}.fa-times-rectangle:before,.fa-window-close:before{content:"\f2d3"}.fa-times-rectangle-o:before,.fa-window-close-o:before{content:"\f2d4"}.fa-bandcamp:before{content:"\f2d5"}.fa-grav:before{content:"\f2d6"}.fa-etsy:before{content:"\f2d7"}.fa-imdb:before{content:"\f2d8"}.fa-ravelry:before{content:"\f2d9"}.fa-eercast:before{content:"\f2da"}.fa-microchip:before{content:"\f2db"}.fa-snowflake-o:before{content:"\f2dc"}.fa-superpowers:before{content:"\f2dd"}.fa-wpexplorer:before{content:"\f2de"}.fa-meetup:before{content:"\f2e0"}.sr-only{position:absolute;width:1px;height:1px;padding:0;margin:-1px;overflow:hidden;clip:rect(0, 0, 0, 0);border:0}.sr-only-focusable:active,.sr-only-focusable:focus{position:static;width:auto;height:auto;margin:0;overflow:visible;clip:auto}
/*]]>*/</style>

    <!-- SqFramework -->
    <script>/*<![CDATA[*/window['$']=function(j){var f=document['querySelectorAll'](j);if(f['length']>0x1)return f;else return f['length']==0x0?document['createDocumentFragment']()['childNodes']:f[0x0];},Element['prototype']['siblings']=function(j){var f=this,D=[...this['parentElement']['children']],k=D['indexOf'](this);return D['filter'](function(q){return j?D['indexOf'](q)!=k&&Array['prototype']['indexOf']['call'](f['parentElement']['querySelectorAll'](j),q)!=-0x1:D['indexOf'](q)!=k;});},Element['prototype']['is']=function(j){return Array['prototype']['indexOf']['call'](document['querySelectorAll'](j),this)!=-0x1;},Element['prototype']['nextUntil']=function(j){return f(this['nextElementSibling']);function f(D){return D&&D['is'](j)?D:f(D['nextElementSibling']);}},Element['prototype']['parentsUntil']=function(j){var f=[];D(this['parentElement']);function D(k){f['push'](k),!k['is'](j)&&D(k['parentElement']);}return f;},Element['prototype']['found']=function(){return!![];},NodeList['prototype']['found']=function(){return this['length']>0x0;},window['oneScroll']=function(j){var f=!![];window['addEventListener']('scroll',function(D){if(f){f=![];if(j)j(D);}});},NodeList['prototype']['filter']=function(j){return Array['prototype']['filter']['call'](this,j);},Element['prototype']['each']=function(j){return j(this);},NodeList['prototype']['each']=function(j){return Array['prototype']['forEach']['call'](this,j);},Array['prototype']['each']=function(j){return Array['prototype']['forEach']['call'](this,j);},HTMLCollection['prototype']['each']=function(j){return Array['prototype']['forEach']['call'](this,j);},Element['prototype']['onClick']=function(j){this['addEventListener']('click',j);},NodeList['prototype']['onClick']=function(j){this['each'](function(f){f['addEventListener']('click',j);});},Element['prototype']['before']=function(j){this[typeof j=='object'?'insertAdjacentElement':'insertAdjacentHTML']('beforebegin',j);},Element['prototype']['after']=function(j){this[typeof j=='object'?'insertAdjacentElement':'insertAdjacentHTML']('afterend',j);},Element['prototype']['prepend']=function(j){this[typeof j=='object'?'insertAdjacentElement':'insertAdjacentHTML']('afterbegin',j);},Element['prototype']['append']=function(j){this[typeof j=='object'?'insertAdjacentElement':'insertAdjacentHTML']('beforeend',j);},Element['prototype']['hasClass']=function(j){return this['classList']['contains'](j);},Element['prototype']['addClass']=function(j){this['classList']['add'](j);},NodeList['prototype']['addClass']=function(j){this['each'](function(f){f['classList']['add'](j);});},Element['prototype']['removeClass']=function(j){this['classList']['remove'](j);},NodeList['prototype']['removeClass']=function(j){this['each'](function(f){f['classList']['remove'](j);});},Element['prototype']['toggleClass']=function(j){this['hasClass'](j)?this['removeClass'](j):this['addClass'](j);},NodeList['prototype']['toggleClass']=function(j){this['each'](function(f){f['hasClass'](j)?f['removeClass'](j):f['addClass'](j);});},Element['prototype']['css']=function(j){if(typeof j=='string')return window['getComputedStyle'](this)[j];if(typeof j=='object')for(var f in j){this['style'][f]=j[f];}},Element['prototype']['slideUp']=function(f,D){var k=this;if(!k['hasClass']('nomove')){k['addClass']('nomove'),k['css']({'transition':(f||0x190)+'ms\x20linear','height':k['offsetHeight']+'px'}),k['offsetHeight'];var q={};q['height']=0x0,q['paddingTop']=0x0,q['paddingBottom']=0x0,q['marginTop']=0x0,q['marginBottom']=0x0,k['css'](q),setTimeout(function(){k['removeAttribute']('style'),k['style']['display']='none',k['removeClass']('nomove');if(D)D(k);},f||0x190);}},Element['prototype']['slideDown']=function(D,k){var q=this;if(!q['hasClass']('nomove')){q['addClass']('nomove'),q['style']['display']='block';var C=q['offsetHeight'],h=q['css']('paddingTop'),s=q['css']('paddingBottom'),G=q['css']('marginTop'),P=q['css']('marginBottom'),Q={};Q['transition']='0s\x20linear',Q['height']=0x0,Q['paddingTop']=0x0,Q['paddingBottom']=0x0,Q['marginTop']=0x0,Q['marginBottom']=0x0,q['css'](Q),q['offsetHeight'];var t={};t['transition']=(D||0x190)+'ms\x20linear',t['height']=C+'px',t['paddingTop']=h,t['paddingBottom']=s,t['marginTop']=G,t['marginBottom']=P,q['css'](t),setTimeout(function(){q['removeAttribute']('style'),q['style']['display']='block',q['removeClass']('nomove');if(k)k(q);},D||0x190);}},Element['prototype']['fadeIn']=function(D,k){var q=this,C={};C['display']='block',C['opacity']=0x0,q['css'](C),q['offsetHeight'];var h={};h['transition']=(D||0x190)+'ms\x20linear',h['opacity']=0x1,q['css'](h),setTimeout(function(){q['removeAttribute']('style'),q['style']['display']='block';if(k)k(q);},D||0x190);},Element['prototype']['fadeOut']=function(f,D){var k=this;k['css']({'opacity':k['css']('opacity')}),k;var q={};q['transition']=(f||0x190)+'ms\x20linear',q['opacity']=0x0,k['css'](q),setTimeout(function(){k['removeAttribute']('style'),k['style']['display']='none';if(D)D(k);},f||0x190);},Element['prototype']['offset']=function(){var f={};return f['top']=this['getBoundingClientRect']()['top']+window['scrollY'],f['bottom']=this['getBoundingClientRect']()['top']+window['scrollY']+this['outerHeight'],f;};function $getJSON(j,f){fetch(j)['then'](D=>{D['json']()['then'](function(k){f(k);});});};function $getScript(j,f,D){var k=document['createElement']('script');k['src']=j,k['onload']=function(){f();};if(D)k[D]=D;document['head']['append'](k);};function $getJSONP(j,f){var D=document['createElement']('script'),k=new URLSearchParams(j)['get']('callback');!k&&(k='sq_'+Math['round'](Math['random']()*0xe8d4a51000),j+='&callback='+k),D['src']=j,window[k]=function(q){f(q);},document['head']['append'](D);};function $load(j,f){fetch(j)['then'](function(D){return D['text']();})['then'](function(D){f(D);});};/*]]>*/</script>

    <b:comment>Default Markups</b:comment>
    <b:defaultmarkups>
      <b:defaultmarkup type='ContactForm'>
        <b:includable id='content'><form autocomplete='off' name='contact-form' spellcheck='false'><input autocomplete='name' class='contact-form-name' expr:id='data:widget.instanceId + "_contact-form-name"' name='name' required='required' type='text'/><i class='fa fa-user'/><label expr:for='data:widget.instanceId + "_contact-form-name"'><data:contactFormNameMsg/></label><input autocomplete='email' class='contact-form-email' expr:id='data:widget.instanceId + "_contact-form-email"' name='email' required='required' type='text'/><i class='fa fa-envelope'/><label expr:for='data:widget.instanceId + "_contact-form-email"'><data:contactFormEmailMsg/></label><textarea class='contact-form-email-message notr' expr:id='data:widget.instanceId + "_contact-form-email-message"' name='email-message' required='required'/><i class='fa fa-quote-right'/><label expr:for='data:widget.instanceId + "_contact-form-email-message"'><data:contactFormMessageMsg/></label><input class='contact-form-button contact-form-button-submit' expr:id='data:widget.instanceId + "_contact-form-submit"' expr:value='data:contactFormSendMsg' type='button'/><div class='contact-state'><p class='contact-form-error-message' expr:id='data:widget.instanceId + "_contact-form-error-message"'/><p class='contact-form-success-message' expr:id='data:widget.instanceId + "_contact-form-success-message"'/></div><div class='clear'/></form></b:includable>
      </b:defaultmarkup>
      <b:defaultmarkup type='LinkList'>
        <b:includable id='ACC'><div class='widget-content accordion-widget'><b:loop index='index' values='data:links' var='link'><div expr:class='data:index == 0 ? "opened acc-head" : "acc-head"'><data:link.name/></div><div class='acc-body notr'><data:link.target/></div></b:loop></div></b:includable>
        <b:includable id='SOC'><ul class='social-widget social'><b:loop values='data:links' var='link'><li><a expr:href='data:link.target' expr:title='data:link.name' rel='nofollow noreferrer' target='_blank'><data:link.name/></a></li></b:loop></ul></b:includable>
        <b:includable id='GAL'><div class='gallery-widget'><b:loop values='data:links' var='link'><a expr:href='data:link.name' target='_blank'><img expr:alt='data:link.name' expr:src='data:link.target' expr:title='data:link.name'/></a></b:loop></div></b:includable>
        <b:includable id='DEF'><nav class='widget-content'><ul><b:loop values='data:links' var='link'><li><a expr:href='data:link.target' expr:title='data:link.name'><data:link.name/></a></li></b:loop></ul></nav></b:includable>
        <b:includable id='AUTH'><b:if cond='data:widget.instanceId == "LinkList500"'><b:tag name='script' type='text/javascript'><b:loop values='data:links' var='link'>AuthorsInfo['<data:link.name/>']='<data:link.target.jsEscaped/>';</b:loop></b:tag><b:else/><b:tag name='script' type='text/javascript'>(function(){var snapAuthor=AuthorsInfo.filter(function(a){return a.name==='<data:title/>'})[0];if(snapAuthor!==undefined){snapAuthor.provided=true;<b:loop values='data:links' var='link'><b:if cond='data:link.name contains "-ad"'>snapAuthor['<data:link.name/>']='<data:link.target.jsEscaped/>';<b:else/><b:switch var='data:link.name'><b:case value='rank'/>snapAuthor.rank='<data:link.target.escaped/>';<b:case value='about'/>snapAuthor.about='<data:link.target.escaped/>';<b:default/>snapAuthor.links['<data:link.name/>']='<data:link.target/>';</b:switch></b:if></b:loop>}})();</b:tag></b:if></b:includable>
        <b:includable id='content'>
          <b:if cond='data:title contains "[ACC]"'><b:include name='ACC'/>
            <b:elseif cond='data:title contains "[SOC]"'/><b:include name='SOC'/>
            <b:elseif cond='data:title contains "[GAL]"'/><b:include name='GAL'/>
            <b:elseif cond='data:widget.sectionId == "Auth-Sec"'/><b:include name='AUTH'/>
            <b:else/><b:include name='DEF'/>
          </b:if>
        </b:includable>
      </b:defaultmarkup>
      <b:defaultmarkup type='Common'>
        <b:includable id='DefaultMeta'>
          <meta content='text/html; charset=UTF-8' http-equiv='Content-Type'/>
          <meta content='width=device-width, initial-scale=1' name='viewport'/>
          <link expr:href='data:view.url.canonical' rel='canonical'/>
          <meta expr:content='data:view.description.escaped' name='description'/>
          <link async='async' expr:href='data:blog.isMobileRequest ? data:blog.blogspotFaviconUrl params {m : 1} : data:blog.blogspotFaviconUrl' rel='icon' type='image/x-icon'/>
          <meta content='IE=edge' http-equiv='X-UA-Compatible'/>
          <meta content='blogger' name='generator'/>
          <meta expr:content='data:skin.vars.body_background_color' name='theme-color'/>
          <meta expr:content='data:skin.vars.body_background_color' name='msapplication-navbutton-color'/>
          <meta expr:content='data:blog.blogId' name='BlogId'/>
          <b:eval expr='data:blog.openIdOpTag'/>
          <b:if cond='data:view.featuredImage'>
            <link expr:href='data:view.featuredImage' rel='image_src'/>
            <b:else/>&lt;link href='<b:include name='altImage'/>' rel='image_src'/&gt;
          </b:if>
          <!--[if IE]><script type='text/javascript'>(function(){var html5=("abbr,article,aside,audio,canvas,datalist,details,"+"figure,footer,header,hgroup,mark,menu,meter,nav,output,"+"progress,section,time,video").split(',');for(var i=0;i<html5.length;i++){document.createElement(html5[i])}try{document.execCommand('BackgroundImageCache',false,true)}catch(e){}})()</script><![endif]-->
        </b:includable>
        <b:includable id='OpenGraph'>
          <meta expr:content='data:blog.localeUnderscoreDelimited == "ar" ? "ar_AR" : data:blog.localeUnderscoreDelimited' property='og:locale'/>
          <meta expr:content='data:view.url.canonical' property='og:url'/>
          <meta expr:content='data:view.title.escaped' property='og:title'/>
          <meta expr:content='data:blog.title.escaped' property='og:site_name'/>
          <meta expr:content='data:view.description.escaped' property='og:description'/>
          <meta expr:content='data:view.title.escaped' property='og:image:alt'/>
          <b:if cond='data:view.isMultipleItems'><meta content='website' property='og:type'/>
            <b:elseif cond='data:view.isSingleItem'/><meta content='article' property='og:type'/>
          </b:if>
          <b:if cond='data:view.featuredImage'>
            <meta expr:content='resizeImage(data:view.featuredImage, 1200, "1200:630")' property='og:image'/>
            <b:else/>&lt;meta content='<b:include name='altImage'/>' property='og:image'/&gt;</b:if>
        </b:includable>
        <b:includable id='TwitterCard'>
          <meta content='summary_large_image' name='twitter:card'/>
          <meta expr:content='data:blog.homepageUrl' name='twitter:domain'/>
          <meta expr:content='data:view.description.escaped' name='twitter:description'/>
          <meta expr:content='data:view.title.escaped' name='twitter:title'/>
          <b:if cond='data:view.featuredImage'>
            <meta expr:content='resizeImage(data:view.featuredImage, 1200, "1200:630")' name='twitter:image'/>
            <b:else/>&lt;meta content='<b:include name='altImage'/>' name='twitter:image'/&gt;</b:if>
        </b:includable>
        <b:includable id='DNSPrefetech'>
          <link as='image' expr:href='data:blog.isMobileRequest ? data:blog.blogspotFaviconUrl params {m : 1} : data:blog.blogspotFaviconUrl' rel='prefetch'/><b:if cond='!data:blog.isMobileRequest and data:skin.vars.body_background.image'><link as='image' expr:href='data:skin.vars.body_background.image' rel='preload'/></b:if><link href='https://script.google.com' rel='dns-prefetch'/><link href='https://fonts.gstatic.com' rel='dns-prefetch'/><link href='https://fonts.googleapis.com' rel='dns-prefetch'/><link href='https://1.bp.blogspot.com' rel='dns-prefetch'/><link href='https://2.bp.blogspot.com' rel='dns-prefetch'/><link href='https://3.bp.blogspot.com' rel='dns-prefetch'/><link href='https://4.bp.blogspot.com' rel='dns-prefetch'/><link href='https://cdnjs.cloudflare.com' rel='dns-prefetch'/><link href='https://pagead2.googlesyndication.com' rel='dns-prefetch'/><link href='https://accounts.google.com' rel='dns-prefetch'/><link href='https://resources.blogblog.com' rel='dns-prefetch'/><b:if cond='data:view.isSingleItem'><link as='image' expr:href='data:view.featuredImage' rel='preload'/><link href='https://connect.facebook.net' rel='dns-prefetch'/><link href='https://www.facebook.com' rel='dns-prefetch'/><link href='https://disqus.com' rel='dns-prefetch'/><link href='https://c.disquscdn.com' rel='dns-prefetch'/></b:if><link as='font' crossorigin='anonymous' href='https://fonts.gstatic.com/s/cairo/v6/SLXGc1nY6HkvalIkTpu0xg.woff2' rel='preload'/><link as='font' crossorigin='anonymous' href='https://fonts.gstatic.com/s/cairo/v6/SLXGc1nY6HkvalIvTpu0xg.woff2' rel='preload'/><link as='font' crossorigin='anonymous' href='https://fonts.gstatic.com/s/cairo/v6/SLXGc1nY6HkvalIhTps.woff2' rel='preload'/><link as='font' crossorigin='anonymous' href='https://cdnjs.cloudflare.com/ajax/libs/font-awesome/4.7.0/fonts/fontawesome-webfont.woff2?v=4.7.0' rel='preload'/><link as='script' href='https://www.google-analytics.com/analytics.js' rel='preload'/>
        </b:includable>
        <b:includable id='widget-title'> 
          <b:if cond='data:widget.sectionId in ["section1", "section2", "section3", "section4", "section5", "section6", "section7", "section8", "section9", "section10", "section11", "section12", "section13", "section14", "section15", "section16", "section17", "section18", "section19", "section20", "section21"]'>
            <b:if cond='data:title'><div class='headline'><h2><data:title/></h2></div></b:if>
            <b:else/>
            <b:if cond='data:title'><div class='headline' expr:data-title='data:title'><h4><data:title/></h4></div></b:if>
          </b:if>
        </b:includable>
        <b:includable id='altImage'>https://3.bp.blogspot.com/-IJ_rjKmn02E/WmSZO9HrscI/AAAAAAAAABA/u8N0vyd9Gogf_kEkkFaDxYrCyh96Lj4cACLcBGAs/s1600-e90-rw/alternate.png</b:includable>
        <b:includable id='altAuthor'>https://3.bp.blogspot.com/-zA1pdXqIA3g/WmSZNYYtVMI/AAAAAAAAAAw/hE9ko5Mhh6Q3Rwj3ziWErOuwLjeekF3IwCLcBGAs/s1600/Unknown.png</b:includable>
      </b:defaultmarkup>
      <b:defaultmarkup type='PopularPosts,FeaturedPost'>
        <b:includable id='snippetedPostContent'><b:if cond='data:widget.type == "PopularPosts"'><time class='post-date'><i class='fa fa-calendar-times-o'/><b:eval expr='format(data:post.date, "dd MMMM YYYY")'/></time></b:if><b:if cond='data:postDisplay.showFeaturedImage'><a class='item-thumbnail PLHolder' expr:href='data:post.url' expr:title="data:post.title.escaped"><b:if cond='data:post.featuredImage'><b:if cond='data:widget.type == "PopularPosts"'><img expr:alt='data:messages.image' expr:data-src='resizeImage(data:post.featuredImage, 72, "1:1")' width='72' height='72'/><b:elseif cond='data:widget.type == "FeaturedPost"'/><img expr:alt='data:messages.image' expr:data-src='resizeImage(data:post.featuredImage, 300, "3:2")' width='300' height='150'/></b:if><b:else/>&lt;img data-src='<b:include name='altImage'/>' alt='<data:messages.image/>'/&gt;</b:if></a></b:if><b:if cond='data:postDisplay.showTitle'><h3 class='post-title'><a expr:href='data:post.url'><data:post.title/></a></h3></b:if><b:if cond='data:widget.type == "FeaturedPost"'><div class='details'><b:if cond='data:widgets.Blog.first.allBylineItems.author'><div class='post-author'><b:if cond='data:post.author.profileUrl'><a expr:href='data:post.author.profileUrl' rel='nofollow noreferrer'><i class='fa fa-user-circle'/><data:post.author.name/></a><b:else/><i class='fa fa-user-circle'/><data:post.author.name/></b:if></div></b:if><b:if cond='data:widgets.Blog.first.allBylineItems.timestamp'><time class='post-date'><i class='fa fa-calendar-times-o'/><b:eval expr='format(data:post.date, "dd MMMM YYYY")'/></time></b:if></div></b:if><b:if cond='data:postDisplay.showSnippet'><p class='snippet-item'><b:if cond='data:widget.type == "PopularPosts"'><b:eval expr='data:post.snippets.long snippet { length: 120, links: false, linebreaks: false }'/><b:elseif cond='data:widget.type == "FeaturedPost"'/><b:eval expr='data:post.snippets.long snippet { length: 240, links: false, linebreaks: false }'/></b:if></p></b:if></b:includable>
      </b:defaultmarkup>
      <b:defaultmarkup type='FollowByEmail'>
        <b:includable id='content'>
          <div class='subscrib-sec'>
            <form action='https://feedburner.google.com/fb/a/mailverify' autocomplete='off' expr:onsubmit='"window.open(\"https://feedburner.google.com/fb/a/mailverify?uri=" + data:feedPath + "\", \"popupwindow\", \"scrollbars=yes,width=550,height=520\"); return true"' method='post' spellcheck='false' target='popupwindow'>
              <label data-trans='0' expr:for='data:widget.instanceId + "-input"'/>
              <input class='follow-by-email-address' expr:id='data:widget.instanceId + "-input"' name='email' placeholder='Email address...' type='text'/>
              <button class='msg-send'><data:messages.subscribe/></button>
              <input expr:value='data:feedPath' name='uri' type='hidden'/>
              <input expr:value='data:blog.locale' name='loc' type='hidden'/>
            </form>
          </div>
        </b:includable>
      </b:defaultmarkup>
      <b:defaultmarkup type='Label'>
        <b:includable id='main' var='this'><b:include name='widget-title'/><b:include name='content'/></b:includable>
        <b:includable id='cloud'><div expr:class='data:this.display + "-label-widget-content"'><b:loop values='data:labels' var='label'><span class='label-size'><a class='label-name' expr:href='data:label.url' expr:title='data:label.name'><data:label.name/></a></span></b:loop></div></b:includable>
        <b:includable id='content'><b:include cond='data:this.display == "list"' name='list'/><b:include cond='data:this.display == "cloud"' name='cloud'/></b:includable>
        <b:includable id='list'><nav expr:aria-label='data:title' expr:class='data:this.display + "-label-widget-content"'><ul><b:loop values='data:labels' var='label'><li><a class='label-name' expr:href='data:label.url' expr:title='data:label.name'><data:label.name/></a><b:if cond='data:this.showFreqNumbers'><span class='label-count'><data:label.count/></span></b:if></li></b:loop></ul></nav></b:includable>
      </b:defaultmarkup>
      <b:defaultmarkup type='BlogArchive'>
        <b:includable id='main' var='this'><b:include name='widget-title'/><b:include name='content'/></b:includable>
        <b:includable id='content'><div class='widget-content'><div id='ArchiveList'><div expr:id='data:widget.instanceId + "_ArchiveList"'><b:include cond='data:this.style == "HIERARCHY"' name='hierarchy'/><b:include cond='data:this.style in {"FLAT", "MENU"}' name='flat'/></div></div></div></b:includable>
        <b:includable id='flat'><nav expr:aria-label='data:title'><ul class='flat'><b:loop values='data:data' var='i'><li class='archivedate'><a expr:href='data:i.url'><data:i.name/><span class='post-count'><data:i.post-count/></span></a></li></b:loop></ul></nav></b:includable>
        <b:includable id='hierarchy'><b:include data='data' name='interval'/></b:includable>
        <b:includable id='interval' var='intervals'><ul class='hierarchy'><b:loop values='data:intervals' var='interval'><li class='archivedate'><div class='hierarchy-title'><a class='post-count-link' expr:href='data:interval.url'><data:interval.name/><span class='post-count'><data:interval.post-count/></span></a></div><div class='hierarchy-content'><b:include cond='data:interval.data' data='interval.data' name='interval'/><b:include cond='data:interval.posts' data='interval.posts' name='posts'/></div></li></b:loop></ul></b:includable>
        <b:includable id='posts' var='posts'><nav expr:aria-label='data:title'><ul class='posts hierarchy'><b:loop values='data:posts' var='post'><li><b:eval expr='data:post.first'/><a expr:href='data:post.url'><data:post.title/></a></li></b:loop></ul></nav></b:includable>
      </b:defaultmarkup>
      <b:defaultmarkup type='PageList'>
        <b:includable id='main'><b:include name='widget-title'/><b:include name='content'/></b:includable>
        <b:includable id='content'><nav class='widget-content'><b:include name='pageList'/></nav></b:includable>
        <b:includable id='overflowButton'><b:include name='verticalMoreIcon'/></b:includable>
        <b:includable id='overflowablePageList'><div class='overflowable-container'><div class='overflowable-contents'><div class='container'><b:with value='true' var='overflow'><b:with value='"tabs"' var='pageListClass'><b:include name='pageList'/></b:with></b:with></div></div><div class='overflow-button hidden'><b:include name='overflowButton'/></div></div></b:includable>
        <b:includable id='pageLink'><li><b:class cond='data:overflow' name='overflowable-item'/><b:class cond='data:link.isCurrentPage' name='selected'/><a expr:href='data:link.href' expr:title='data:link.title'><data:link.title/></a></li></b:includable>
        <b:includable id='pageList'><nav><ul><b:class cond='data:pageListClass' expr:name='data:pageListClass'/><b:loop values='data:links' var='link'><b:include name='pageLink'/></b:loop></ul></nav></b:includable>
      </b:defaultmarkup>
    </b:defaultmarkups>

    <b:if cond='data:blog.adsenseClientId'>&lt;!--</b:if>&lt;/head&gt;&lt;!--</head><b:if cond='!data:blog.adsenseClientId'>--&gt;</b:if>
  <body>
    <b:class expr:name='data:blog.pageType'/>
    <b:class name='boxed'/>
    <b:class cond='data:view.isHomepage' name='Homepage'/>
    <b:class expr:name='data:blog.languageDirection'/>
    <b:attr name='data-overflow' value='false'/>
    <b:class name='notr'/>


    <!-- Spinner -->
    <div class='Loading hide notr'><div class='spinner notr'><div class='bounce1'/><div class='bounce2'/><div class='bounce3'/></div></div>

    <!-- Darkmode -->
    <div class='b-toggles'>
      <span/>
      <button class='hide' data-trans-title='38' id='bt-scheme' title='Contrast'><svg><use href='#darkicon'/></svg><svg><use href='#lighticon'/></svg></button>
      <button class='hide' data-trans-title='37' id='bt-boxing' title='BackStyle'><i class='fa fa-compress'/><i class='fa fa-expand'/></button>
    </div>

    <!-- Main Container -->
    <div expr:class='(data:view.isLayoutMode ? data:blog.languageDirection : "") + " main-container"'>
      <b:section id='Tempnec' showaddelement='false'>
        <b:widget id='BlogArchive400' locked='true' title='BlogArchive' type='BlogArchive' version='2' visible='true'>
          <b:widget-settings>
            <b:widget-setting name='showStyle'>FLAT</b:widget-setting>
            <b:widget-setting name='yearPattern'>yyyy</b:widget-setting>
            <b:widget-setting name='showWeekEnd'>true</b:widget-setting>
            <b:widget-setting name='monthPattern'>MMMM yyyy</b:widget-setting>
            <b:widget-setting name='dayPattern'>MMM dd</b:widget-setting>
            <b:widget-setting name='weekPattern'>MM/dd</b:widget-setting>
            <b:widget-setting name='chronological'>false</b:widget-setting>
            <b:widget-setting name='showPosts'>false</b:widget-setting>
            <b:widget-setting name='frequency'>MONTHLY</b:widget-setting>
          </b:widget-settings>
          <b:includable id='main'><b:tag name='script' type='text/javascript'>var PostCount=<b:eval expr='data:data map (d=&gt; d.post-count)'/>.reduce(function(a,b){return a+b});</b:tag></b:includable>
          <b:includable id='content'/>
          <b:includable id='flat'/>
          <b:includable id='hierarchy'/>
          <b:includable id='interval' var='intervals'/>
          <b:includable id='posts' var='posts'/>
        </b:widget>
        <b:widget id='Label400' locked='true' title='BlogLabels' type='Label' version='2' visible='true'>
          <b:widget-settings>
            <b:widget-setting name='sorting'>ALPHA</b:widget-setting>
            <b:widget-setting name='display'>LIST</b:widget-setting>
            <b:widget-setting name='selectedLabelsList'/>
            <b:widget-setting name='showType'>ALL</b:widget-setting>
            <b:widget-setting name='showFreqNumbers'>true</b:widget-setting>
          </b:widget-settings>
          <b:includable id='main' var='this'><b:if cond='data:labels'><script>var _bl=<b:eval expr='data:labels map (l =&gt; l.name.jsEscaped + ":" + l.count)'/></script><ul class='hide' itemscope='itemscope' itemtype='http://www.schema.org/SiteNavigationElement'><b:loop values='data:labels' var='label'><li><a expr:href='data:label.url' itemprop='url'><span itemprop='name'><data:label.name/></span></a></li></b:loop></ul><b:else/><script>var _bl={Labels:0}</script></b:if></b:includable>
          <b:includable id='cloud'/>
          <b:includable id='content'/>
          <b:includable id='list'/>
        </b:widget>
        <b:widget cond='data:view.isSingleItem' id='Profile400' locked='true' title='BlogAuthors' type='Profile' version='2' visible='true'>
          <b:widget-settings>
            <b:widget-setting name='showaboutme'>true</b:widget-setting>
            <b:widget-setting name='showlocation'>true</b:widget-setting>
          </b:widget-settings>
          <b:includable id='main'><b:tag name='script' type='text/javascript'>var AuthorsInfo=new Array();<b:if cond='data:team'><b:loop values='data:authors' var='author'>AuthorsInfo.push({"name":"<data:author.display-name/>","avatar":"<b:include data='author' name='authorImageTeam'/>","count":0,"links":{}});</b:loop><b:else/>AuthorsInfo.push({"name":"<data:displayname/>","avatar":"<b:include name='authorImageSingle'/>","count":0,"links":{}})</b:if></b:tag></b:includable>
          <b:includable id='authorImageSingle'><b:if cond='data:authorPhoto.image'><data:authorPhoto.image/><b:else/><b:include name='altAuthor'/></b:if></b:includable>
          <b:includable id='authorImageTeam' var='author'><b:if cond='data:author.authorPhoto.image'><data:author.authorPhoto.image/><b:else/><b:include name='altAuthor'/></b:if></b:includable>
          <b:includable id='authorProfileImage'/>
          <b:includable id='content'/>
          <b:includable id='defaultProfileImage'/>
          <b:includable id='profileImage'/>
          <b:includable id='teamProfile'/>
          <b:includable id='teamProfileLink'/>
          <b:includable id='userGoogleProfile'/>
          <b:includable id='userLocation'/>
          <b:includable id='userProfile'/>
          <b:includable id='userProfileData'/>
          <b:includable id='userProfileImage'/>
          <b:includable id='userProfileInfo'/>
          <b:includable id='userProfileLink'/>
          <b:includable id='userProfileText'/>
          <b:includable id='viewProfileLink'/>
        </b:widget>
        <b:widget id='HTML400' locked='true' title='كود التفعيل' type='HTML' version='2' visible='true'>
          <b:includable id='main'><script>var LicenseKey='<data:content/>'</script></b:includable>
        </b:widget>
        <b:widget id='LinkList400' locked='true' title='إعدادات القالب' type='LinkList' version='2' visible='true'>
          <b:includable id='main'><b:include name='content'/></b:includable>
          <b:includable id='ACC'/>
          <b:includable id='AUTH'/>
          <b:includable id='DEF'/>
          <b:includable id='GAL'/>
          <b:includable id='SOC'/>
          <b:includable id='content'><b:tag name='script' type='text/javascript'>var SqCmz={<b:loop index='i' values='data:links' var='link'>'<data:link.name/>':<b:if cond='data:link.target in {true,false}'><data:link.target/><b:else/>'<data:link.target/>'</b:if><b:if cond='data:links.length gt (data:i+1)'>,</b:if></b:loop>}</b:tag></b:includable>
        </b:widget>
      </b:section>
      <script>/*<![CDATA[*/var SqCmz=SqCmz||{};if(!AuthorsInfo){var AuthorsInfo=[]}var trans=[];if(SqCmz['spinner']===true){$('.Loading').removeClass('hide')}else{document.body.setAttribute('data-overflow',true);}if(SqCmz['scheme-button']!=false){$('#bt-scheme').removeClass('hide');}if(localStorage.isDark=='true'){document.body.addClass('dm');}if(SqCmz['boxing-button']!=false){$('#bt-boxing').removeClass('hide');}if(localStorage.isBoxed==undefined){if(SqCmz['wide-background']==true){document.body.removeClass('boxed');}}else{if(localStorage.isBoxed=='false'){document.body.removeClass('boxed');}}document.body.addClass('pl-' + (SqCmz['img-appear']||'fade'));if(SqCmz['v-index']===true){document.body.classList.add("v-index")}/*]]>*/</script>

      <!-- Header -->
      <header>
        <div class='color-wrap'>
          <b:section class='wrapper' id='top-bar' maxwidgets='3' showaddelement='false'>
            <b:widget id='PageList301' locked='true' title='الصفحات' type='PageList' version='2' visible='true'>
              <b:includable id='main'><b:include name='content'/></b:includable>
              <b:includable id='content'><nav class='menu' expr:area-label='data:title'><ul><b:loop values='data:links' var='link'><li><b:class cond='data:link.isCurrentPage' name='selected'/><a expr:href='data:link.href' expr:title='data:link.title'><data:link.title/></a></li></b:loop></ul></nav><div class='menu-res'><button aria-label='menu toggle'><i class='fa fa-bars'/></button><nav class='menu-res-wrap'><ul><b:loop values='data:links' var='link'><li><a expr:href='data:link.href' expr:title='data:link.title'><data:link.title/></a></li></b:loop></ul></nav></div></b:includable>
              <b:includable id='overflowButton'/>
              <b:includable id='overflowablePageList'/>
              <b:includable id='pageLink'/>
              <b:includable id='pageList'/>
            </b:widget>
            <b:widget id='LinkList301' locked='true' title='مواقع التواصل الإجتماعي' type='LinkList' version='2' visible='true'>
              <b:widget-settings>
                <b:widget-setting name='text-0'>facebook</b:widget-setting>
                <b:widget-setting name='link-0'>#</b:widget-setting>
                <b:widget-setting name='text-1'>twitter</b:widget-setting>
                <b:widget-setting name='link-1'>#</b:widget-setting>
                <b:widget-setting name='text-2'>youtube</b:widget-setting>
                <b:widget-setting name='link-2'>#</b:widget-setting>
                <b:widget-setting name='text-3'>tiktok</b:widget-setting>
                <b:widget-setting name='link-3'>#</b:widget-setting>
                <b:widget-setting name='text-4'>pinterest</b:widget-setting>
                <b:widget-setting name='link-4'>#</b:widget-setting>
                <b:widget-setting name='text-5'>rss</b:widget-setting>
                <b:widget-setting name='link-5'>#</b:widget-setting>
                <b:widget-setting name='sorting'>NONE</b:widget-setting>
              </b:widget-settings>
              <b:includable id='main'><b:include name='content'/></b:includable>
              <b:includable id='ACC'/>
              <b:includable id='AUTH'/>
              <b:includable id='DEF'/>
              <b:includable id='GAL'/>
              <b:includable id='SOC'/>
              <b:includable id='content'><b:if cond='data:view.isHomepage'><b:tag name='script' type='application/ld+json'>{"@context": "http://schema.org","@type": "Organization","url": "<data:blog.homepageUrl/>","logo": "<b:loop values='data:links' var='link'><b:if cond='data:link.name in {"logo","Logo"}'><data:link.target/></b:if></b:loop>","name": "<data:blog.title/>","sameAs":[<b:loop index='i' values='data:links' var='link'><b:if cond='data:link.name not in {"logo","Logo"}'>"<data:link.target/>"<b:if cond='data:links.length != data:i+1'>,</b:if></b:if></b:loop>]}</b:tag></b:if><nav expr:area-label='data:title'><ul class='social-static social'><b:loop values='data:links' var='link'><b:if cond='data:link.name in ["khamsat","mostaql","tradent","google-play","messenger","blogger","discord","tiktok","patreon"]'><li><a expr:href='data:link.target' expr:title='data:link.name' rel='nofollow noreferrer' target='_blank'><svg expr:class='"fa-" + data:link.name'><use expr:href='"#ic-"+data:link.name'/></svg></a></li><b:else/><li><a expr:href='data:link.target' expr:title='data:link.name' rel='nofollow noreferrer' target='_blank'><i expr:class='"fa fa-" + data:link.name'/></a></li></b:if></b:loop></ul></nav></b:includable>
            </b:widget>
            <b:widget id='HTML301' locked='true' title='مربع البحث' type='HTML' version='2' visible='true'>
              <b:widget-settings>
                <b:widget-setting name='content'/>
              </b:widget-settings>
              <b:includable id='main'><b:include name='content'/></b:includable>
              <b:includable id='content'><b:if cond='data:view.isHomepage'><b:tag name='script' type='application/ld+json'>{"@context": "http://schema.org","@type": "WebSite","url": "<data:blog.homepageUrl/>","potentialAction": [{"@type": "SearchAction","target": "<data:blog.searchUrl/>?q={search_term_string}","query-input": "required name=search_term_string"}]}</b:tag></b:if><form action='/search' autocomplete='off' class='search' role='search'><label for='searchInput'><input expr:placeholder='data:messages.searchThisBlog' expr:value='data:view.search.query' id='searchInput' name='q' required='required' spellcheck='false' type='text'/><b><data:messages.searchThisBlog/></b></label><button expr:title='data:messages.search' type='button'><i class='fa fa-search'/></button></form></b:includable>
            </b:widget>
          </b:section>
        </div>

        <b:section class='wrapper' id='head-sec' maxwidgets='2' showaddelement='false'>
          <b:widget id='Header1' locked='true' title='Squeeze Demo (رأس الصفحة)' type='Header' version='1'>
            <b:includable id='main'><b:include name='content'/></b:includable>
            <b:includable id='content'><div class='logo'><b:if cond='data:useImage'><a class='img-logo' expr:href='data:blog.homepageUrl' expr:title='data:blog.title'><b:if cond='data:sourceUrl'><b:comment>قم بتفعيل السطر التالي في حالة عدم وجود شعار بجودة عالية</b:comment><!--<img expr:alt='data:blog.title' expr:src='data:sourceUrl' expr:title='data:blog.title'/>--><b:comment>قم بتعطيل السطر التالي في حالة عدم وجود شعار بجودة عالية</b:comment><img expr:alt='data:blog.title' expr:src='data:sourceUrl' expr:srcset='sourceSet(data:sourceUrl, [360,480,640,992,1150,1600,1920], "2900:860")' expr:title='data:blog.title' height='90' width='300'/></b:if></a><b:if cond='data:view.isSingleItem'><h2 class='hide'><data:blog.title/></h2><b:else/><h1 class='hide'><data:blog.title/></h1></b:if><b:else/><div class='txt-logo'><b:include name='title'/><b:include name='description'/></div></b:if></div></b:includable>
            <b:includable id='description'><p class='description'><data:description/></p></b:includable>
            <b:includable id='title'><b:if cond='data:view.isSingleItem'><h2 class='headone'><a expr:href='data:blog.homepageUrl' expr:title='data:blog.title'><data:title/></a></h2><b:else/><h1 class='headone'><data:title/></h1></b:if></b:includable>
          </b:widget>
          <b:widget cond='!data:blog.isMobileRequest' id='HTML302' locked='true' title='إعلان سطح المكتب' type='HTML' version='2' visible='true'>
            <b:includable id='main'><b:include name='content'/></b:includable>
            <b:includable id='content'><div class='widget-content'><data:content/></div></b:includable>
          </b:widget>
          <b:widget cond='data:blog.isMobileRequest' id='HTML307' locked='true' title='إعلان الهاتف' type='HTML' version='2' visible='true'>
            <b:includable id='main'><b:include name='content'/></b:includable>
            <b:includable id='content'><div class='widget-content'><data:content/></div></b:includable>
          </b:widget>
        </b:section>


        <b:section class='wrapper' id='menu-bar' maxwidgets='1' showaddelement='false'>
          <b:widget id='LinkList302' locked='true' title='القائمة الرئيسية' type='LinkList' version='2' visible='true'>
            <b:includable id='main'><a class='res-home' data-trans='1' expr:href='data:blog.homepageUrl' expr:title='data:messages.home'><svg><use href='#home'/></svg></a><nav class='menu-bar' expr:area-label='data:title'><button class='menu-bar-res' expr:title='data:title'><i class='fa fa-bars'/></button><ul><li><a class='home' expr:href='data:blog.homepageUrl' expr:title='data:messages.home'><span data-trans='1'/></a></li><b:loop index='index' values='data:links' var='link'><b:if cond='data:link.name contains "_"'><b:if cond='data:link.name contains "__"'><li class='ssitem'><a expr:href='data:link.target' expr:title='data:link.name'><data:link.name/></a></li><b:else/><b:if cond='data:index != 0'><b:eval expr='data:links[data:index - 1].name contains "_" ? "&lt;/ul&gt;&lt;/li&gt;" : ""'/></b:if>&lt;li class='bot-menu-st sitem'&gt;<a expr:href='data:link.target' expr:title='data:link.name'><data:link.name/></a><b:eval expr='data:links[data:index + 1].name contains "_" ? "&lt;ul&gt;" : "&lt;/li&gt;"'/></b:if><b:else/><b:if cond='data:index != 0'><b:eval expr='data:links[data:index - 1].name contains "__" ? "&lt;/ul&gt;&lt;/li&gt;" : ""'/><b:eval expr='"&lt;/ul&gt;&lt;/li&gt;"'/></b:if><b:if cond='data:link.target contains "#" and data:link.target.size &gt; 1'>&lt;li class='MegaItem'&gt;<a expr:href='data:link.target' expr:title='data:link.name'><data:link.name/></a><div class='mega-wrap'/><b:else/>&lt;li class='drop-menu-st'&gt;<a expr:href='data:link.target' expr:title='data:link.name'><data:link.name/></a></b:if><b:eval expr='data:index+1 == data:links.length ? "&lt;/li&gt;" : "&lt;ul&gt;"'/></b:if></b:loop></ul></nav></b:includable>
            <b:includable id='ACC'/>
            <b:includable id='AUTH'/>
            <b:includable id='DEF'/>
            <b:includable id='GAL'/>
            <b:includable id='SOC'/>
            <b:includable id='content'/>
          </b:widget>
        </b:section>
        <i class='clear'/>
      </header>

      <!-- Intro -->
      <div class='intro wrapper hide'>
        <b:section id='section300' showaddelement='false'>
          <b:widget id='HTML305' locked='true' title='أخبار ساخنة' type='HTML' version='2' visible='true'>
            <b:widget-settings>
              <b:widget-setting name='content'>recent</b:widget-setting>
            </b:widget-settings>
            <b:includable id='main'><b:include name='content'/></b:includable>
            <b:includable id='content'><i class='ord hide'><data:content/></i><div class='ticker' id='tick'><div class='ticker-title'><data:title/></div><div class='ticker-content'/></div></b:includable>
          </b:widget>
        </b:section>
        <b:if cond='data:view.isHomepage'>
          <b:section class='wrapper' id='section301' showaddelement='false'>
            <b:widget id='HTML306' locked='true' title='السلايدر الرئيسي' type='HTML' version='2' visible='true'>
              <b:widget-settings>
                <b:widget-setting name='content'>random</b:widget-setting>
              </b:widget-settings>
              <b:includable id='main'><b:include name='content'/></b:includable>
              <b:includable id='content'><i class='ord hide'><data:content/></i><div class='main-slider'><div class='m-slider splide notr' id='m-slider'><div class='splide__track notr'><div class='splide__list notr'/></div></div><div class='left-box'><!-- Top --><div class='top'/><!-- Bottom --><div class='bottom'/></div></div><i class='clear'/></b:includable>
            </b:widget>
          </b:section>
        </b:if>
        <i class='clear'/>
      </div>

      <div class='main-wrap wrapper'>

        <!-- Homepage Sections -->
        <b:if cond='data:view.isHomepage'>
          <div class='top-content cate wide-sec'>
            <!-- Home Section -->
            <div class='sided-sections cate'>
              <b:section id='section1'/>
              <b:section id='section2'/>
              <b:section id='section3'/>
              <i class='clear'/>
            </div>

            <!-- Home Section -->
            <div class='sided-sections cate opt-before'>
              <b:section id='section4'/>
              <b:section id='section5'/>
              <b:section id='section6'/>
              <i class='clear'/>
            </div>

            <!-- Home Section -->
            <div class='sided-sections cate opt-before'>
              <b:section id='section7'/>
              <b:section id='section8'/>
              <b:section id='section9'/>
              <i class='clear'/>
            </div>
          </div>
        </b:if>

        <!-- Start Middle Content -->
        <div class='middle-content'>
          <main expr:class='"notr side-" + data:skin.vars.startSide'>

            <!-- Homepage Sections -->
            <b:if cond='data:view.isHomepage'>
              <div class='sided-sections cate'>
                <b:section id='section10'/>
                <b:section id='section11'/>
                <i class='clear'/>
              </div>
            </b:if>

            <!-- Main Blog Section -->
            <b:section id='RecentPosts' preferred='true' showaddelement='false'>
              <b:widget cond='data:view.isMultipleItems and !data:view.isHomepage' id='HTML505' locked='true' title='إعلان صفحات التسميات والأرشيف والبحث' type='HTML' version='2' visible='true'>
                <b:includable id='main'><b:include name='content'/></b:includable>
                <b:includable id='content'><div class='widget-content'><data:content/></div></b:includable>
              </b:widget>
              <b:widget id='Blog1' locked='true' title='رسائل المدونة الإلكترونية' type='Blog' version='2' visible='true'>
                <b:widget-settings>
                  <b:widget-setting name='showDateHeader'>false</b:widget-setting>
                  <b:widget-setting name='style.textcolor'>#ffffff</b:widget-setting>
                  <b:widget-setting name='showShareButtons'>true</b:widget-setting>
                  <b:widget-setting name='authorLabel'>بواسطة</b:widget-setting>
                  <b:widget-setting name='showCommentLink'>true</b:widget-setting>
                  <b:widget-setting name='style.urlcolor'>#ffffff</b:widget-setting>
                  <b:widget-setting name='showAuthor'>true</b:widget-setting>
                  <b:widget-setting name='style.linkcolor'>#ffffff</b:widget-setting>
                  <b:widget-setting name='style.unittype'>TextAndImage</b:widget-setting>
                  <b:widget-setting name='style.bgcolor'>#ffffff</b:widget-setting>
                  <b:widget-setting name='timestampLabel'/>
                  <b:widget-setting name='reactionsLabel'/>
                  <b:widget-setting name='showAuthorProfile'>false</b:widget-setting>
                  <b:widget-setting name='style.layout'>1x1</b:widget-setting>
                  <b:widget-setting name='showLabels'>true</b:widget-setting>
                  <b:widget-setting name='showLocation'>false</b:widget-setting>
                  <b:widget-setting name='postLabelsLabel'/>
                  <b:widget-setting name='showTimestamp'>true</b:widget-setting>
                  <b:widget-setting name='postsPerAd'>1</b:widget-setting>
                  <b:widget-setting name='showBacklinks'>false</b:widget-setting>
                  <b:widget-setting name='style.bordercolor'>#ffffff</b:widget-setting>
                  <b:widget-setting name='showInlineAds'>false</b:widget-setting>
                  <b:widget-setting name='showReactions'>false</b:widget-setting>
                </b:widget-settings>
                <b:includable id='main' var='this'>
                  <b:tag name='div'>
                    <b:attr cond='!data:view.isError and !data:view.isHomepage' name='class' value='hfeed'/>
                    <b:if cond='data:view.isHomepage'>
                      <div class='headline'><h2><a expr:title='data:messages.recentPosts' href='/search'><data:messages.recentPosts/></a></h2><a class='ribble' href='/search'><span><data:messages.showMore/></span></a></div>
                      <b:elseif cond='data:view.isArchive'/>
                      <div class='headline'><h2><data:messages.archive/></h2></div>
                    </b:if>
                    <b:include name='status-message'/> 
                    <b:tag name='div'>
                      <b:class cond='data:view.isMultipleItems' name='index-posts'/>
                      <b:class cond='data:view.isPage' name='static-page'/>
                      <b:class cond='data:view.isPost' name='item-page'/>
                      <b:class cond='data:view.isError' name='error-page'/>
                      <b:attr cond='data:view.isMultipleItems' name='role' value='feed'/>
                      <b:if cond='data:posts.length &gt; 0'>
                        <b:include cond='data:view.isMultipleItems' data='posts' name='indexMeta'/>
                        <b:loop index='i' values='data:posts' var='post'>
                          <b:include cond='data:view.isSingleItem' data='post' name='postMeta'/>
                          <article class='post-outer' expr:aria-labelledby='data:post.title.escaped'>
                            <b:class cond='data:view.isSingleItem' name='hentry'/>
                            <b:include data='post' name='post'/>
                          </article>
                        </b:loop>
                      </b:if>
                    </b:tag>
                    <b:if cond='data:view.isMultipleItems'>
                      <b:if cond='data:blog.feedLinks.length gt 3'>
                        <div id='Pagination'/>
                        <b:else/>
                        <b:include name='nextprev'/>
                      </b:if>
                    </b:if>
                  </b:tag>
                </b:includable>
                <b:includable id='aboutPostAuthor'/>
                <b:includable id='addComments'/>
                <b:includable id='blogThisShare'>
                  <b:with value='"window.open(this.href, \"_blank\", \"height=270,width=475\"); return false;"' var='onclick'>
                    <b:include name='platformShare'/>
                  </b:with>
                </b:includable>
                <b:includable id='bylineByName' var='byline'>
                  <b:switch var='data:byline.name'>
                    <b:case value='share'/>
                    <b:include cond='data:post.shareUrl' name='postShareButtons'/>
                    <b:case value='comments'/>
                    <b:include cond='data:post.allowComments' name='postCommentsLink'/>
                    <b:case value='location'/>
                    <b:include cond='data:post.location' name='postLocation'/>
                    <b:case value='timestamp'/>
                    <b:include cond='not data:view.isPage' name='postTimestamp'/>
                    <b:case value='author'/>
                    <b:include name='postAuthor'/>
                    <b:case value='labels'/>
                    <b:include cond='data:post.labels' name='postLabels'/>
                    <b:case value='icons'/>
                    <b:include cond='data:post.emailPostUrl' name='emailPostIcon'/>
                  </b:switch>
                </b:includable>
                <b:includable id='bylineRegion' var='regionItems'>
                  <b:loop values='data:regionItems' var='byline'>
                    <b:include data='byline' name='bylineByName'/>
                  </b:loop>
                </b:includable>
                <b:includable id='commentAuthorAvatar'/>
                <b:includable id='commentDeleteIcon' var='comment'/>
                <b:includable id='commentForm' var='post'>
                  <div class='comment-form'>
                    <a name='comment-form'/>
                    <h4 id='comment-post-message'><data:messages.postAComment/></h4>
                    <b:if cond='data:this.messages.blogComment != ""'>
                      <p><data:this.messages.blogComment/></p>
                    </b:if>
                    <b:include data='post' name='commentFormIframeSrc'/>
                    <iframe allowtransparency='allowtransparency' class='blogger-iframe-colorize blogger-comment-from-post' expr:height='data:cmtIframeInitialHeight ?: "95px"' frameborder='0' id='comment-editor' name='comment-editor' src='' title='comment form' width='100%'/>
                    <data:post.cmtfpIframe/>
                    <script type='text/javascript'>
                      BLOG_CMT_createIframe('<data:post.appRpcRelayPath/>');
                    </script>
                  </div>
                </b:includable>
                <b:includable id='commentFormIframeSrc' var='post'>
                  <a expr:href='data:post.commentFormIframeSrc + "&amp;skin=contempo"' id='comment-editor-src' title='comment form link'/>
                </b:includable>
                <b:includable id='commentItem' var='comment'><li class='comment' expr:id='"c" + data:comment.id'><div class='avatar-image-container'><b:if cond='data:comment.authorAvatarSrc contains "blank.gif"'><span class='noimg'/><b:else/><img expr:alt='data:comment.author + " photo"' expr:data-src='resizeImage(data:comment.authorAvatarSrc, ((data:comment.inReplyTo and data:comment.inReplyTo != "0") ? "40" : "72") , "1:1")' expr:title='data:comment.author + " " + data:messages.photo'/></b:if></div><div class='comment-block'><div class='comment-header'><cite expr:class='data:comment.author == data:post.author.name ? "user blog-author" : "user"'><b:if cond='data:comment.authorUrl'><a expr:href='data:comment.authorUrl' rel='nofollow noreferrer' target='_blank'><data:comment.author/></a><b:else/><span><data:comment.author/></span></b:if></cite><span class='datetime com-date' expr:data-date='data:comment.timestampAbs'><data:comment.timestamp/></span></div></div><p class='comment-content'><data:comment.body/></p><span class='comment-actions secondary-text'>
                  <b:if cond='!data:comment.inReplyTo or data:comment.inReplyTo == "0"'><span><button class='comment-reply' expr:data-comment-id='data:comment.id'><data:messages.postAComment/></button></span></b:if><span expr:class='data:comment.adminClass'><a expr:href='data:comment.deleteUrl' rel='nofollow noreferrer' target='_blank'><data:messages.deleteComment/></a></span></span><div class='comment-replies'><ul><b:loop values='data:post.comments where (c =&gt; c.inReplyTo == data:comment.id)' var='reply'><b:include data='reply' name='commentItem'/></b:loop></ul></div></li></b:includable>
                <b:includable id='commentList' var='comments'/>
                <b:includable id='commentPicker' var='post'>
                  <section class='topic-comments' id='item-comments'>
                    <div class='headline'><h4><data:messages.comments/></h4></div>
                    <div class='comments-bar' expr:aria-label='data:messages.comments' role='tablist'/>
                    <div class='comments-tabs'/>
                    <div class='comments' id='comments'>
                      <b:tag name='script'>var AllowComments=<b:eval expr='data:post.allowComments'/></b:tag>
                      <b:if cond='data:post.allowComments'>
                        <b:tag name='script'>var AllowNew=<data:post.allowNewComments/></b:tag>
                        <b:include data='post' name='threadedComments'/>
                      </b:if>
                    </div>
                  </section>
                </b:includable>
                <b:includable id='comments' var='post'>
                  <ul><b:loop values='data:post.comments where (c =&gt; not c.inReplyTo or c.inReplyTo == "0")' var='comment'><b:include data='comment' name='commentItem'/></b:loop></ul>
                  <b:if cond='data:post.commentPagingRequired'><div id='loadmore'><a class='ribble'><span><data:messages.showMore/></span></a></div></b:if>
                </b:includable>
                <b:includable id='commentsLink'/>
                <b:includable id='commentsLinkIframe'>
                  <!-- G+ comments, no longer available. The includable is retained for backwards-compatibility. -->
                </b:includable>
                <b:includable id='commentsTitle'/>
                <b:includable id='defaultAdUnit'>
                  <ins class='adsbygoogle' data-ad-format='auto' expr:data-ad-client='data:adClientId ?: data:blog.adsenseClientId' expr:data-ad-host='data:blog.adsenseHostId' expr:data-analytics-uacct='data:blog.analyticsAccountNumber' expr:style='data:style ?: "display: block;"'/>
                  <script>
                    (adsbygoogle = window.adsbygoogle || []).push({});
                  </script>
                </b:includable>
                <b:includable id='emailPostIcon'>
                  <span class='byline post-icons'>
                    <!-- email post links -->
                    <span class='item-action'>
                      <a expr:href='data:post.emailPostUrl' expr:title='data:messages.emailPost'>
                        <b:include data='{ iconClass: "touch-icon sharing-icon" }' name='emailIcon'/>
                      </a>
                    </span>
                  </span>
                </b:includable>
                <b:includable id='facebookShare'>
                  <b:with value='"window.open(this.href, \"_blank\", \"height=430,width=640\"); return false;"' var='onclick'>
                    <b:include name='platformShare'/>
                  </b:with>
                </b:includable>
                <b:includable id='feedLinks'/>
                <b:includable id='feedLinksBody' var='links'/>
                <b:includable id='footerBylines'>
                  <b:if cond='data:widgets.Blog.first.footerBylines'>
                    <b:loop index='i' values='data:widgets.Blog.first.footerBylines' var='region'>
                      <b:if cond='not data:region.items.empty'>
                        <div expr:class='"post-footer-line post-footer-line-" + (data:i + 1)'>
                          <b:with value='"footer-" + (data:i + 1)' var='regionName'>
                            <b:include data='region.items' name='bylineRegion'/>
                          </b:with>
                        </div>
                      </b:if>
                    </b:loop>
                  </b:if>
                </b:includable>
                <b:includable id='googlePlusShare'></b:includable>
                <b:includable id='headerByline'>
                  <b:if cond='data:widgets.Blog.first.headerByline'>
                    <div class='post-header'>
                      <div class='post-header-line-1'>
                        <b:with value='"header-1"' var='regionName'>
                          <b:include data='data:widgets.Blog.first.headerByline.items' name='bylineRegion'/>
                        </b:with>
                      </div>
                    </div>
                  </b:if>
                </b:includable>
                <b:includable id='homePageLink'/>
                <b:includable id='iframeComments' var='post'/>
                <b:includable id='indexMeta' var='posts'>
                  <b:tag name='script' type='application/ld+json'>{"@context":"http://schema.org","@type":"ItemList","itemListElement":[<b:loop index='i' values='data:posts' var='post'>{"@type":"ListItem","position":<b:eval expr='data:i + 1'/>,"url":"<data:post.url/>"}<b:if cond='data:i != data:posts.length - 1'>,</b:if></b:loop>]}</b:tag>
                  <b:if cond='data:view.isLabelSearch'>
                    <b:tag cond='data:view.isLabelSearch' name='script' type='application/ld+json'>{"@context":"http://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"item":{"@id":"<data:blog.homepageUrl/>","name":"<data:blog.title/>"}},{"@type":"ListItem","position":2,"item":{"@id":"<data:blog.searchUrl/>","name":"search"}},{"@type":"ListItem","position":3,"item":{"@id":"<data:view.url.canonical/>","name":"<data:view.search.label/>"}}]}</b:tag>
                  </b:if>
                </b:includable>
                <b:includable id='inlineAd' var='post'/>
                <b:includable id='linkShare'>
                  <b:with value='"window.prompt(\"Copy to clipboard: Ctrl+C, Enter\", \"" + data:originalUrl + "\"); return false;"' var='onclick'>
                    <b:include name='platformShare'/>
                  </b:with>
                </b:includable>
                <b:includable id='nextPageLink'/>
                <b:includable id='nextprev'>
                  <div class='old-pagination'>
                    <b:if cond='data:newerPageUrl'>
                      <a class='blog-pager-newer-link' expr:href='data:newerPageUrl' expr:title='data:messages.newerPosts'><data:messages.newerPosts/></a>
                    </b:if>
                    <b:if cond='data:olderPageUrl'>
                      <a class='blog-pager-older-link' expr:href='data:olderPageUrl' expr:title='data:messages.olderPosts'><data:messages.olderPosts/></a>
                    </b:if>
                  </div>
                </b:includable>
                <b:includable id='otherSharingButton'>
                  <span class='sharing-platform-button sharing-element-other' expr:aria-label='data:messages.shareToOtherApps.escaped' expr:data-url='data:originalUrl' expr:title='data:messages.shareToOtherApps.escaped' role='menuitem' tabindex='-1'>
                    <b:with value='{key: "sharingOther"}' var='platform'>
                      <b:include name='sharingPlatformIcon'/>
                    </b:with>
                    <span class='platform-sharing-text'><data:messages.shareOtherApps.escaped/></span>
                  </span>
                </b:includable>
                <b:includable id='p-post-index'>
                  <!-- Index -->
                  <a class='img-wrap PLHolder' expr:href='data:post.url' expr:title='data:post.title'>
                    <b:if cond='data:post.thumbnailUrl'>
                      <img expr:alt='data:post.title' expr:data-src='data:post.thumbnailUrl' expr:title='data:post.title'/>
                      <b:else/>
                      &lt;img data-src='<b:include name='altImage'/>' alt='<data:post.title/>' title='<data:post.title/>'/&gt;
                    </b:if>
                    <div class='overlay'/>
                    <span class='label-title'><data:post.labels.first.name/></span>
                  </a>
                  <h3 class='post-title cate-link'><a class='entry-title' expr:href='data:post.url' itemprop='name'><data:post.title/></a></h3>
                  <div class='details'>
                    <b:include data='post' name='sq-author'/>
                    <b:include data='post' name='sq-timestamp'/>
                  </div>       
                  <p class='cate-snippet'><b:if cond='data:blog.isMobileRequest'><b:eval expr='data:post.snippets.long snippet { length: 92, ellipsis: true, links: false, linebreaks: false }'/><b:else/><b:eval expr='data:post.snippets.long snippet { length: 110 , ellipsis: true, links: false, linebreaks: false }'/>
                    </b:if></p>
                  <b:include data='post' name='sq-sharePost'/>
                  <a class='read-more' expr:href='data:post.url'><data:blog.jumpLinkMessage/></a>
                </b:includable>
                <b:includable id='p-post-item'>
                  <!-- article-ad -->
                  <div class='article-ad str-ad'/>
                  <!-- Posts -->
                  <header>
                    <h1 class='entry-title topic-title'><data:post.title/></h1>
                    <!-- Post Details -->
                    <div class='topic-tools topic-details'>
                      <b:include data='post' name='sq-timestamp'/>
                      <b:include data='post' name='sq-author'/>
                      <b:include data='post' name='sq-labels'/>
                    </div>

                    <!-- Font Sizer -->
                    <div class='topic-tools zooming'>
                      <i class='fa fa-plus'/>
                      <span data-trans='34'/>
                      <i class='fa fa-minus'/>
                    </div>
                  </header>
                  <!-- article-ad -->
                  <div class='article-ad top-ad'/>
                  <!-- Table of Contents -->
                  <section id='TOC'/>
                  <!-- Post Body -->
                  <div class='post-body entry-content entry-summary notr'><data:post.body/></div>
                  <!-- article-ad -->
                  <div class='article-ad bot-ad'/>
                  <div class='post-pages'/>
                  <footer>
                    <b:include data='post' name='sq-postQuickEdit'/>
                    <b:include data='post' name='sq-sharePost'/>
                    <b:include data='post' name='sq-reactions'/>
                    <b:include data='post' name='sq-authorAbout'/>
                    <b:include data='post' name='sq-navigation'/>

                    <!-- article-ad -->
                    <div class='article-ad rlt-ad'/>

                    <!-- Related Posts -->
                    <section class='topic-related'><div class='headline'><h4><data:messages.youMayLikeThesePosts/></h4></div><div class='related-carousel splide notr'><div class='splide__track notr'><div class='splide__list notr' role='feed'/></div></div></section>

                    <!-- article-ad -->
                    <div class='article-ad cmt-ad'/>

                    <!-- Comments -->
                    <b:include data='post' name='commentPicker'/>

                    <!-- article-ad -->
                    <div class='article-ad end-ad'/>

                    <b:include data='post' name='sq-scripts'/>
                  </footer>
                </b:includable>
                <b:includable id='p-post-static-page' var='post'>
                  <!-- Pages -->
                  <header class='headline'>
                    <h1 class='entry-title' itemprop='name'><data:post.title/></h1>
                  </header>
                  <div class='post-body entry-content entry-summary'><data:post.body/></div>
                  <footer>
                    <b:include data='post' name='sq-postQuickEdit'/>

                    <!-- Comments -->
                    <b:include data='post' name='commentPicker'/>

                    <div class='hide'>
                      <b:include data='post' name='sq-author'/>
                      <b:include data='post' name='sq-timestamp'/>
                      <b:include data='post' name='sq-scripts'/>
                    </div>
                  </footer>
                </b:includable>
                <b:includable id='platformShare'>
                  <a expr:class='"goog-inline-block sharing-" + data:platform.key' expr:data-url='data:originalUrl' expr:href='data:shareUrl + "&amp;target=" + data:platform.target' expr:onclick='data:onclick ? data:onclick : ""' expr:title='data:platform.shareMessage' target='_blank'>
                    <span class='share-button-link-text'>
                      <data:platform.shareMessage/>
                    </span>
                  </a>
                </b:includable>
                <b:includable id='post' var='post'>
                  <b:switch var='data:blog.pageType'>
                    <b:case value='index'/><b:include data='post' name='p-post-index'/>
                    <b:case value='archive'/><b:include data='post' name='p-post-index'/>
                    <b:case value='item'/><b:include data='post' name='p-post-item'/>
                    <b:case value='static_page'/><b:include data='post' name='p-post-static-page'/>
                    <b:case value='archive'/><b:include data='post' name='p-post-index'/>
                  </b:switch>
                </b:includable>
                <b:includable id='postAuthor'>
                  <span class='byline post-author vcard'>
                    <span class='post-author-label'>
                      <data:byline.label/>
                    </span>
                    <span class='fn'>
                      <b:if cond='data:post.author.profileUrl'>
                        <meta expr:content='data:post.author.profileUrl'/>
                        <a class='g-profile' expr:href='data:post.author.profileUrl' rel='author' title='author profile'>
                          <span><data:post.author.name/></span>
                        </a>
                        <b:else/>
                        <span><data:post.author.name/></span>
                      </b:if>
                    </span>
                  </span>
                </b:includable>
                <b:includable id='postBody' var='post'/>
                <b:includable id='postBodySnippet' var='post'/>
                <b:includable id='postCommentsAndAd' var='post'/>
                <b:includable id='postCommentsLink'/>
                <b:includable id='postFooter' var='post'/>
                <b:includable id='postFooterAuthorProfile' var='post'/>
                <b:includable id='postHeader' var='post'/>
                <b:includable id='postJumpLink' var='post'/>
                <b:includable id='postLabels'>
                  <span class='byline post-labels'>
                    <span class='byline-label'><data:byline.label/></span>
                    <b:loop index='i' values='data:post.labels' var='label'>
                      <a expr:href='data:label.url' rel='tag'>
                        <data:label.name/>
                      </a>
                    </b:loop>
                  </span>
                </b:includable>
                <b:includable id='postLocation'>
                  <span class='byline post-location'>
                    <data:byline.label/>
                    <a expr:href='data:post.location.mapsUrl' target='_blank'><data:post.location.name/></a>
                  </span>
                </b:includable>
                <b:includable id='postMeta'>
                  <b:tag name='script' type='application/ld+json'>
                    {"@context":"http://schema.org","@type":"BlogPosting","headline":"<data:post.title.escaped/>","name":"<data:post.title.escaped/>","inLanguage":"ar","datePublished":"<data:post.date.iso8601.jsonEscaped/>","dateModified":"<data:post.lastUpdated.iso8601.jsonEscaped/>","articleBody":"<b:eval expr='data:post.snippets.long.escaped'/>","description":"<b:eval expr='data:blog.metaDescription ? data:blog.metaDescription.escaped : data:post.snippets.long.escaped'/>",<b:if cond='data:post.labels'>"keywords":"<b:loop index='l' values='data:post.labels' var='label'><data:label.name/><b:if cond='data:l != data:post.labels.length - 1'>,</b:if></b:loop>",</b:if> "author" :{"@context":"http://schema.org","@type":"Person","image":"<data:post.author.authorPhoto.image/>","jobTitle":"Author","name":"<data:post.author.name.jsonEscaped/>","url":"<b:eval cond='data:post.author.profileUrl' expr='data:post.author.profileUrl'/>"},"image":{"@context":"http://schema.org","@type":"ImageObject","contentUrl":"<data:post.url/>","url":"<b:if cond='data:post.featuredImage'><b:eval expr='resizeImage(data:post.featuredImage,"1200","1200:630")'/><b:else/><b:include name='altImage'/></b:if>","name":"<data:post.title.jsonEscaped/>","width":630,"height":1200},"publisher":{"@context":"http://schema.org/","@type":"Organization","name":"Blogger","logo":{"@context":"http://schema.org/","@type":"ImageObject","url":"https://lh3.googleusercontent.com/ULB6iBuCeTVvSjjjU1A-O8e9ZpVba6uvyhtiWRti_rBAs9yMYOFBujxriJRZ-A=w206-h60","width":206,"height":60}},"mainEntityOfPage":{"@context":"http://schema.org/","@type":"WebPage","id":"<data:post.url.canonical.jsonEscaped/>"}}
                  </b:tag>
                  <b:if cond='data:view.isPost'>
                    <b:tag name='script' type='application/ld+json'>{"@context":"http://schema.org","@type":"BreadcrumbList","itemListElement":[{"@type":"ListItem","position":1,"item":{"@id":"<data:blog.homepageUrl/>","name":"<data:blog.title/>"}},<b:if cond='data:post.labels'>{"@type":"ListItem","position":2,"item":{"@id":"<data:post.labels.first.url/>","name":"<data:post.labels.first.name/>"}},<b:else/>{"@type":"ListItem","position":2,"item":{"@id":"<data:post.date.year/>","name":"<data:post.date.year/>"}},</b:if>{"@type":"ListItem","position":3,"item":{"@id":"<data:post.url/>","name":"<data:post.title.escaped/>"}}]}</b:tag>
                  </b:if>
                </b:includable>
                <b:includable id='postMetadataJSONImage'>
                  "image": {
                  "@type": "ImageObject",
                  <b:if cond='data:post.featuredImage.isResizable'>
                    "url": "<b:eval expr='resizeImage(data:post.featuredImage, 1200, "1200:630")'/>",
                    "height": 630,
                    "width": 1200
                    <b:else/>
                    "url": "https://lh3.googleusercontent.com/ULB6iBuCeTVvSjjjU1A-O8e9ZpVba6uvyhtiWRti_rBAs9yMYOFBujxriJRZ-A=w1200",
                    "height": 348,
                    "width": 1200
                  </b:if>
                  },
                </b:includable>
                <b:includable id='postMetadataJSONPublisher'>
                  "publisher": {
                  "@type": "Organization",
                  "name": "Blogger",
                  "logo": {
                  "@type": "ImageObject",
                  "url": "https://lh3.googleusercontent.com/ULB6iBuCeTVvSjjjU1A-O8e9ZpVba6uvyhtiWRti_rBAs9yMYOFBujxriJRZ-A=h60",
                  "width": 206,
                  "height": 60
                  }
                  },
                </b:includable>
                <b:includable id='postPagination'/>
                <b:includable id='postReactions'>
                  <!-- Reaction feature no longer available. The includable is retained for backwards-compatibility. -->
                </b:includable>
                <b:includable id='postShareButtons'>
                  <div class='byline post-share-buttons goog-inline-block'>
                    <b:with value='data:sharingId ?: ((data:widget.instanceId ?: "sharing") + "-" + (data:regionName ?: "byline") + "-" + data:post.id)' var='sharingId'>
                      <!-- Note: 'sharingButtons' includable is from the default Sharing widget markup. -->
                      <b:include data='{                                                sharingId: data:sharingId,                                                originalUrl: data:post.url,                                                platforms: data:this.sharing.platforms,                                                shareUrl: data:post.shareUrl,                                                shareTitle: data:post.title,                                              }' name='sharingButtons'/>
                    </b:with>
                  </div>
                </b:includable>
                <b:includable id='postTimestamp'>
                  <span class='byline post-timestamp'>
                    <data:byline.label/>
                    <b:if cond='data:post.url'>
                      <meta expr:content='data:post.url.canonical'/>
                      <a class='timestamp-link' expr:href='data:post.url' rel='bookmark' title='permanent link'>
                        <time class='published' expr:datetime='data:post.date.iso8601' expr:title='data:post.date.iso8601'>
                          <data:post.date/>
                        </time>
                      </a>
                    </b:if>
                  </span>
                </b:includable>
                <b:includable id='postTitle' var='post'/>
                <b:includable id='previousPageLink'/>
                <b:includable id='sharingButton'>
                  <span expr:aria-label='data:platform.shareMessage' expr:class='"sharing-platform-button sharing-element-" + data:platform.key' expr:data-href='data:shareUrl + "&amp;target=" + data:platform.target' expr:data-url='data:originalUrl' expr:title='data:platform.shareMessage' role='menuitem' tabindex='-1'>
                    <b:include name='sharingPlatformIcon'/>
                    <span class='platform-sharing-text'><data:platform.name/></span>
                  </span>
                </b:includable>
                <b:includable id='sharingButtonContent'>
                  <div class='flat-icon-button ripple'>
                    <b:include name='shareIcon'/>
                  </div>
                </b:includable>
                <b:includable id='sharingButtons'>
                  <div class='sharing' expr:aria-owns='"sharing-popup-" + data:sharingId' expr:data-title='data:shareTitle'>
                    <button class='sharing-button touch-icon-button' expr:aria-controls='"sharing-popup-" + data:sharingId' expr:aria-label='data:messages.share.escaped' expr:id='"sharing-button-" + data:sharingId' role='button'>
                      <b:include name='sharingButtonContent'/>
                    </button>
                    <b:include name='sharingButtonsMenu'/>
                  </div>
                </b:includable>
                <b:includable id='sharingButtonsMenu'>
                  <div class='share-buttons-container'>
                    <ul aria-hidden='true' class='share-buttons hidden' expr:aria-label='data:messages.share.escaped' expr:id='"sharing-popup-" + data:sharingId' role='menu'>
                      <b:loop values='(data:platforms ?: data:blog.sharing.platforms) filter (p =&gt; p.key not in {"blogThis"})' var='platform'>
                        <li>
                          <b:include name='sharingButton'/>
                        </li>
                      </b:loop>
                      <li aria-hidden='true' class='hidden'>
                        <b:include name='otherSharingButton'/>
                      </li>
                    </ul>
                  </div>
                </b:includable>
                <b:includable id='sharingPlatformIcon'>
                  <b:include data='{ iconClass: ("touch-icon sharing-" + data:platform.key) }' expr:name='data:platform.key + "Icon"'/>
                </b:includable>
                <b:includable id='sq-author' var='post'>
                  <b:if cond='data:widgets.Blog.first.allBylineItems.author or data:view.isPage'>
                    <!-- Post Author -->
                    <div>
                      <b:class cond='data:view.isSingleItem' name='vcard'/>
                      <b:if cond='data:post.author.profileUrl'>
                        <a class='author-prof url' expr:href='data:post.author.profileUrl' title='author'>
                          <i class='fa fa-user-circle'/><span class='fn'><data:post.author.name/></span>
                        </a>
                        <b:else/>
                        <span class='fn'><i class='fa fa-user-circle'/><data:post.author.name/></span>
                      </b:if>
                    </div>
                  </b:if>
                </b:includable>
                <b:includable id='sq-authorAbout' var='post'>
                  <!-- Author Profile -->
                  <section class='topic-author'>
                    <div class='author-img'>
                      <b:if cond='data:post.author.authorPhoto.image'>
                        <img alt='author-img' expr:data-src='resizeImage(data:post.author.authorPhoto.image , "90", "1:1" )' title='author-img' width="90" height="90"/>
                        <b:else/>
                        &lt;img class='photo' alt='<data:post.title/>' data-src='<b:include name='altAuthor'/>' title='<data:post.title/>'/&gt;
                      </b:if>
                    </div>
                    <div class='topic-author-name'><data:post.author.name/></div>
                    <i class='clear-left'/>
                    <span class='author-about'/>
                    <div class='social'/>
                    <i class='clear'/>
                  </section>
                </b:includable>
                <b:includable id='sq-labels' var='post'>
                  <b:if cond='data:widgets.Blog.first.allBylineItems.labels and data:post.labels.any'>
                    <!-- Post Labels -->
                    <span class='categ'>
                      <a expr:href='data:blog.homepageUrl' expr:title='data:messages.home'><i class='fa fa-home'/><data:messages.home/></a>
                      <b:loop values='data:post.labels' var='label'>
                        <a expr:href='data:label.url' rel='tag' title='قسم'><i class='fa fa-folder-open'/><data:label.name/></a>
                      </b:loop>
                    </span>
                  </b:if>
                </b:includable>
                <b:includable id='sq-navigation' var='post'>
                  <!-- Navigation -->
                  <div class='topic-nav'>
                    <meta expr:content='data:post.title' name='postTitle'/>
                    <meta expr:content='data:post.thumbnailUrl' name='postPoster'/>
                    <div class='topic-nav-wrap'>
                      <div class='topic-nav-cont' role='feed'>
                        <b:if cond='data:this.newerPageUrl'>
                          <a class='next' expr:href='data:blog.httpsEnabled ? data:this.newerPageUrl.https : data:this.newerPageUrl' expr:title='data:messages.newer' role='article'></a>
                        </b:if>
                        <b:if cond='data:this.olderPageUrl'>
                          <a class='prev' expr:href='data:blog.httpsEnabled ? data:this.olderPageUrl.https : data:this.olderPageUrl' expr:title='data:messages.older' role='article'></a>
                        </b:if>
                      </div>
                    </div>
                  </div>
                </b:includable>
                <b:includable id='sq-postQuickEdit' var='post'>
                  <!-- Quick Edit -->
                  <span expr:class='"edit-post item-control " + data:post.adminClass'/>
                </b:includable>
                <b:includable id='sq-reactions' var='post'>
                  <b:if cond='data:widgets.Blog.first.allBylineItems.reactions'>
                    <!-- Post Reactions -->
                    <span class='reaction-buttons'>
                      <span class='reactions-label'><data:widgets.Blog.first.allBylineItems.reactions.label/></span>
                      <iframe allowtransparency='true' class='reactions-iframe' expr:src='data:post.reactionsUrl' frameborder='0' name='reactions' scrolling='no' title='comment form'/>
                    </span>
                  </b:if>
                </b:includable>
                <b:includable id='sq-scripts' var='post'>
                  <b:tag name='script' type='text/javascript'>var AuthorName="<data:post.author.name/>",PagedPost=<b:eval expr='data:post.body contains "&amp;lt;&amp;gt;&amp;lt;&amp;gt;"'/></b:tag>
                  <b:if cond='data:post.body contains "&amp;lt;&amp;gt;&amp;lt;&amp;gt;"'>
                    <!-- Pagination -->
                    <script type='text/javascript'>/*<![CDATA[*/(function(){var a=document.querySelector(".post-body"),b=a.innerHTML.split("&lt;&gt;&lt;&gt;"),c=1,d=new String,e=location.search.replace("?","").split("&");document.querySelector(".post-outer").classList.add("divided-post");for(var f=0;f<e.length;f++)"page"===e[f].split("=")[0]&&(c=parseInt(e[f].split("=")[1]));a.innerHTML=b[c-1],c!==b.length&&(d+="<a class=\"next-page\" href=\"?page="+(c+1)+"\" data-trans=\"2\"></a>"),1!==c&&(d+="<a class=\"prev-page\" href=\"?page="+(c-1)+"\" data-trans=\"3\"></a>"),document.querySelector(".post-pages").innerHTML=d})();/*]]>*/</script>
                  </b:if>
                  <b:if cond='data:post.body contains "sq-widepost"'>
                    <!-- Remove Sidebar -->
                    <script type='text/javascript'>/*<![CDATA[*/document.body.classList.add("no-sidebar");var RemoveSideBar=setInterval(function(){var aside=document.getElementsByTagName("aside")[0];if(aside!==undefined){clearInterval(RemoveSideBar);aside.parentNode.removeChild(aside) }},0);/*]]>*/</script>
                  </b:if>
                </b:includable>
                <b:includable id='sq-sharePost' var='post'>
                  <b:if cond='data:widgets.Blog.first.allBylineItems.share'>
                    <b:if cond='data:view.isPost'>
                      <!-- Share Post -->
                      <nav class='topic-share'>
                        <ul class='social'>
                          <li><a class='fa fa-facebook' expr:href='data:post.shareUrl + "&amp;target=facebook"' onclick='popUp(event,this)' title='Facebook Share Button'><span data-trans='4'/><span>Facebook</span></a></li>
                          <li><a class='fa fa-twitter' expr:href='data:post.shareUrl + "&amp;target=twitter"' onclick='popUp(event,this)' title='Twitter Share Button'><span data-trans='6'/><span>Twitter</span></a></li>
                          <li><a class='fa fa-linkedin' expr:href='"https://www.linkedin.com/sharing/share-offsite/?url=" + data:post.url' onclick='popUp(event,this)' title='Pinterest Share Button'><span data-trans='8'/><span>LinkedIn</span></a></li>
                          <li><a class='fa fa-pinterest-p' expr:href='data:post.shareUrl + "&amp;target=pinterest"' onclick='popUp(event,this)' title='Pinterest Share Button'><span data-trans='7'/><span>Pinterest</span></a></li>
                          <li><a class='fa fa-whatsapp' expr:href='data:blog.isMobileRequest ? "whatsapp://send?text=" + data:post.title + " : " + data:post.url : "https://api.whatsapp.com/send?text=" + data:post.title + " : " + data:post.url' onclick='popUp(event,this)' title='Whatsapp Share Button'><span data-trans='8'/><span>Whatsapp</span></a></li>
                          <li><a class='fa fa-envelope' expr:href='data:post.shareUrl + "&amp;target=email"' expr:title='data:top.emailThisMsg' onclick='popUp(event,this)' title='Email Button'><span data-trans='9'/><span>Email</span></a></li>
                          <li><a class='fa fa-print' href='javascript:window.print()' title='Print Button'><span data-trans='10'/><span>Print</span></a></li>
                        </ul>
                      </nav>
                    </b:if>
                    <b:if cond='data:view.isMultipleItems'>
                      <div class='post-share'>
                        <div class='share-icon'><i class='fa fa-share-alt'/></div>
                        <ul class='share-menu'>
                          <li><a expr:href='data:post.shareUrl + "&amp;target=facebook"' expr:title='data:blog.sharing.platforms[1].shareMessage' rel='nofollow noopener' target='_blank'></a></li>
                          <li><a expr:href='data:post.shareUrl + "&amp;target=twitter"' expr:title='data:blog.sharing.platforms[3].shareMessage' rel='nofollow noopener' target='_blank'></a></li>
                          <li><a expr:href='data:post.shareUrl + "&amp;target=pinterest"' expr:title='data:blog.sharing.platforms[4].shareMessage' rel='nofollow noopener' target='_blank'></a></li>
                        </ul>
                      </div>
                    </b:if>
                  </b:if>
                </b:includable>
                <b:includable id='sq-timestamp' var='post'>
                  <b:if cond='data:widgets.Blog.first.allBylineItems.timestamp or data:view.isPage'>
                    <!-- Post Date -->
                    <a expr:href='data:post.url' rel='bookmark' title='bookmark'>
                      <time class='post-date' expr:title='data:post.date.iso8601'>
                        <i class='fa fa-calendar-times-o'/>
                        <b:if cond='data:view.isPage'><data:post.date/><b:else/><b:eval expr='format(data:post.date, "dd MMMM YYYY")'/></b:if>
                      </time>
                    </a>
                    <time class='hide published' expr:datetime='data:post.date.iso8601.jsonEscaped'><data:post.date.iso8601.jsonEscaped/></time>
                    <time class='hide updated' expr:datetime='data:post.lastUpdated.iso8601.jsonEscaped'><data:post.lastUpdated.iso8601.jsonEscaped/></time>
                  </b:if>
                </b:includable>
                <b:includable id='status-message'>
                  <b:if cond='data:view.isError'>
                    <div class='ErrorSection'>
                      <span><i class='fa fa-exclamation-triangle'/></span>
                      <h2>خطأ 404</h2>
                      <p><data:navMessage/></p>
                    </div>
                    <b:else/>
                    <b:if cond='data:navMessage'><div class='status-msg-body'><data:navMessage/></div></b:if>
                  </b:if>
                </b:includable>
                <b:includable id='threadedCommentForm' var='post'>
                  <div id='comments-respond'>
                    <h4 id='comment-post-message'><data:messages.postAComment/></h4>
                    <p><data:this.messages.blogComment/></p>
                    <a expr:href='data:post.commentFormIframeSrc + "&amp;skin=contempo"' id='comment-editor-src' title='comment form link'/>
                    <iframe allowtransparency='allowtransparency' class='blogger-iframe-colorize blogger-comment-from-post' expr:data-src='data:post.commentFormIframeSrc + "&amp;skin=contempo"' frameborder='0' height='95px' id='comment-editor' name='comment-editor' title='comment form' width='100%'/>
                    <noscript>&lt;!--<data:post.cmtfpIframe/>--&gt;</noscript>
                  </div>
                </b:includable>
                <b:includable id='threadedCommentJs' var='post'/>
                <b:includable id='threadedComments' var='post'>
                  <section class='comments threaded' expr:data-embed='data:post.embedCommentForm' expr:data-num-comments='data:post.numberOfComments' id='comments-wrap'>
                    <a name='comments'/>
                    <div class='comments-info'>
                      <div class='comments-count'>
                        <b:message name='messages.numberOfComments'>
                          <b:param expr:value='data:post.numberOfComments' name='numComments'/>
                        </b:message>
                      </div>
                      <b:if cond='data:post.allowNewComments == "true"'>
                        <a class='go-respond ribble' href='#comments-respond'><span><data:messages.postAComment/></span></a>
                      </b:if>
                      <div class='comments-show'>
                        <button class='comments-only notr' title="Comments only" data-trans-title="11" data-trans='11'/>
                        <button class='comments-replys active notr' title="Comments replys" data-trans-title="12" data-trans='12'/>
                      </div>
                    </div>
                    <div class='comments-content'>
                      <div class='comments-list' id='comment-holder'><b:include data='post' name='comments'/></div>
                    </div>
                    <b:if cond='data:post.allowNewComments'>
                      <div class='comment-footer'><b:include data='post' name='threadedCommentForm'/></div>
                      <b:else/>
                      <p class='c-not-allowed'><data:post.noNewCommentsText/></p>
                    </b:if>
                    <b:if cond='data:showCmtPopup'>
                      <div id='comment-popup'><iframe allowtransparency='allowtransparency' frameborder='0' id='comment-actions' name='comment-actions' scrolling='no' title='comment form'/></div>
                    </b:if>
                  </section>
                </b:includable>
              </b:widget>
              <b:widget cond='data:view.isPage' id='ContactForm93' locked='true' mobile='yes' title='نموذج الاتصال' type='ContactForm' version='2' visible='true'>
                <b:includable id='main'><b:include name='content'/></b:includable>
                <b:includable id='content'><form autocomplete='off' name='contact-form' spellcheck='false'><input autocomplete='name' class='contact-form-name' expr:id='data:widget.instanceId + "_contact-form-name"' name='name' required='required' type='text'/><i class='fa fa-user'/><label expr:for='data:widget.instanceId + "_contact-form-name"'><data:contactFormNameMsg/></label><input autocomplete='email' class='contact-form-email' expr:id='data:widget.instanceId + "_contact-form-email"' name='email' required='required' type='text'/><i class='fa fa-envelope'/><label expr:for='data:widget.instanceId + "_contact-form-email"'><data:contactFormEmailMsg/></label><textarea class='contact-form-email-message notr' expr:id='data:widget.instanceId + "_contact-form-email-message"' name='email-message' required='required'/><i class='fa fa-quote-right'/><label expr:for='data:widget.instanceId + "_contact-form-email-message"'><data:contactFormMessageMsg/></label><input class='contact-form-button contact-form-button-submit' expr:id='data:widget.instanceId + "_contact-form-submit"' expr:value='data:contactFormSendMsg' type='button'/><div class='contact-state'><p class='contact-form-error-message' expr:id='data:widget.instanceId + "_contact-form-error-message"'/><p class='contact-form-success-message' expr:id='data:widget.instanceId + "_contact-form-success-message"'/></div><div class='clear'/></form></b:includable>
              </b:widget>
            </b:section>

            <!-- Authors Section -->
            <b:if cond='data:view.isLayoutMode or data:view.isSingleItem'>
              <b:section class='hide' id='Auth-Sec'>
                <b:widget id='LinkList500' locked='true' title='Default' type='LinkList' version='2' visible='true'>
                  <b:includable id='main'><b:include name='widget-title'/><b:include name='content'/></b:includable>
                  <b:includable id='ACC'><div class='widget-content accordion-widget'><b:loop index='index' values='data:links' var='link'><div expr:class='data:index == 0 ? "opened acc-head" : "acc-head"'><data:link.name/></div><div class='acc-body notr'><data:link.target/></div></b:loop></div></b:includable>
                  <b:includable id='AUTH'><b:if cond='data:widget.instanceId == "LinkList500"'><b:tag name='script' type='text/javascript'><b:loop values='data:links' var='link'>AuthorsInfo['<data:link.name/>']='<data:link.target.jsEscaped/>';</b:loop></b:tag><b:else/><b:tag name='script' type='text/javascript'>(function(){var snapAuthor=AuthorsInfo.filter(function(a){return a.name==='<data:title/>'})[0];if(snapAuthor!==undefined){snapAuthor.provided=true;<b:loop values='data:links' var='link'><b:if cond='data:link.name contains "-ad"'>snapAuthor['<data:link.name/>']='<data:link.target.jsEscaped/>';<b:else/><b:switch var='data:link.name'><b:case value='rank'/>snapAuthor.rank='<data:link.target.escaped/>';<b:case value='about'/>snapAuthor.about='<data:link.target.escaped/>';<b:default/>snapAuthor.links['<data:link.name/>']='<data:link.target/>';</b:switch></b:if></b:loop>}})();</b:tag></b:if></b:includable>
                  <b:includable id='DEF'><nav class='widget-content'><ul><b:loop values='data:links' var='link'><li><a expr:href='data:link.target' expr:title='data:link.name'><data:link.name/></a></li></b:loop></ul></nav></b:includable>
                  <b:includable id='GAL'><div class='gallery-widget'><b:loop values='data:links' var='link'><a expr:href='data:link.name' target='_blank'><img expr:alt='data:link.name' expr:src='data:link.target' expr:title='data:link.name'/></a></b:loop></div></b:includable>
                  <b:includable id='SOC'><ul class='social-widget social'><b:loop values='data:links' var='link'><li><a expr:href='data:link.target' expr:title='data:link.name' rel='nofollow noreferrer' target='_blank'><data:link.name/></a></li></b:loop></ul></b:includable>
                  <b:includable id='content'>
                    <b:if cond='data:title contains "[ACC]"'><b:include name='ACC'/>
                      <b:elseif cond='data:title contains "[SOC]"'/><b:include name='SOC'/>
                      <b:elseif cond='data:title contains "[GAL]"'/><b:include name='GAL'/>
                      <b:elseif cond='data:widget.sectionId == "Auth-Sec"'/><b:include name='AUTH'/>
                      <b:else/><b:include name='DEF'/>
                    </b:if>
                  </b:includable>
                </b:widget>
              </b:section>
            </b:if>

            <!-- Homepage Sectins -->
            <b:if cond='data:view.isHomepage'>
              <div class='sided-sections cate'>
                <b:section id='section12'/>
                <b:section id='section13'/>
                <i class='clear'/>
              </div>
              <div class='sided-sections cate opt-before'>
                <b:section id='section14'/>
                <b:section id='section15'/>
                <i class='clear'/>
              </div>
            </b:if>
          </main>

          <b:if cond='data:view.isError'>
            <style>.side-right{width:100%;float:none;}</style>
            <b:else/>
            <aside class='notr'>
              <b:section id='sidebar-section'>
                <b:widget id='LinkList2' locked='false' title='التواصل الإجتماعي[SOC]' type='LinkList' version='2' visible='true'>

                  <b:includable id='main'><b:include name='widget-title'/><b:include name='content'/></b:includable>
                  <b:includable id='ACC'><div class='widget-content accordion-widget'><b:loop index='index' values='data:links' var='link'><div expr:class='data:index == 0 ? "opened acc-head" : "acc-head"'><data:link.name/></div><div class='acc-body notr'><data:link.target/></div></b:loop></div></b:includable>
                  <b:includable id='AUTH'><b:if cond='data:widget.instanceId == "LinkList500"'><b:tag name='script' type='text/javascript'><b:loop values='data:links' var='link'>AuthorsInfo['<data:link.name/>']='<data:link.target.jsEscaped/>';</b:loop></b:tag><b:else/><b:tag name='script' type='text/javascript'>(function(){var snapAuthor=AuthorsInfo.filter(function(a){return a.name==='<data:title/>'})[0];if(snapAuthor!==undefined){snapAuthor.provided=true;<b:loop values='data:links' var='link'><b:if cond='data:link.name contains "-ad"'>snapAuthor['<data:link.name/>']='<data:link.target.jsEscaped/>';<b:else/><b:switch var='data:link.name'><b:case value='rank'/>snapAuthor.rank='<data:link.target.escaped/>';<b:case value='about'/>snapAuthor.about='<data:link.target.escaped/>';<b:default/>snapAuthor.links['<data:link.name/>']='<data:link.target/>';</b:switch></b:if></b:loop>}})();</b:tag></b:if></b:includable>
                  <b:includable id='DEF'><nav class='widget-content'><ul><b:loop values='data:links' var='link'><li><a expr:href='data:link.target' expr:title='data:link.name'><data:link.name/></a></li></b:loop></ul></nav></b:includable>
                  <b:includable id='GAL'><div class='gallery-widget'><b:loop values='data:links' var='link'><a expr:href='data:link.name' target='_blank'><img expr:alt='data:link.name' expr:src='data:link.target' expr:title='data:link.name'/></a></b:loop></div></b:includable>
                  <b:includable id='SOC'><ul class='social-widget social'><b:loop values='data:links' var='link'><li><a expr:href='data:link.target' expr:title='data:link.name' rel='nofollow noreferrer' target='_blank'><data:link.name/></a></li></b:loop></ul></b:includable>
                  <b:includable id='content'>
                    <b:if cond='data:title contains "[ACC]"'><b:include name='ACC'/>
                      <b:elseif cond='data:title contains "[SOC]"'/><b:include name='SOC'/>
                      <b:elseif cond='data:title contains "[GAL]"'/><b:include name='GAL'/>
                      <b:elseif cond='data:widget.sectionId == "Auth-Sec"'/><b:include name='AUTH'/>
                      <b:else/><b:include name='DEF'/>
                    </b:if>
                  </b:includable>
                </b:widget>
                <b:widget id='HTML10' locked='false' title='مشاركات عشوائية' type='HTML' version='2' visible='true'>
                  <b:includable id='main'><b:include name='widget-title'/><div class='widget-content'><data:content/></div></b:includable>
                </b:widget>
                <b:widget id='HTML20' locked='false' title='مشاركات عشوائية [رأسي]' type='HTML' version='2' visible='true'>
                  <b:includable id='main'><b:include name='widget-title'/><div class='widget-content'><data:content/></div></b:includable>
                </b:widget>
                <b:widget id='PopularPosts1' locked='false' title='المشاركات الشائعة' type='PopularPosts' version='2' visible='true'>
                  <b:widget-settings>
                    <b:widget-setting name='numItemsToShow'>5</b:widget-setting>
                    <b:widget-setting name='showThumbnails'>true</b:widget-setting>
                    <b:widget-setting name='showSnippets'>true</b:widget-setting>
                    <b:widget-setting name='timeRange'>ALL_TIME</b:widget-setting>
                  </b:widget-settings>
                  <b:includable id='main' var='this'><b:include name='widget-title'/><div class='widget-content'><b:include name='snippetedPosts'/></div></b:includable>
                  <b:includable id='blogThisShare'>
                    <b:with value='"window.open(this.href, \"_blank\", \"height=270,width=475\"); return false;"' var='onclick'>
                      <b:include name='platformShare'/>
                    </b:with>
                  </b:includable>
                  <b:includable id='bylineByName' var='byline'>
                    <b:switch var='data:byline.name'>
                      <b:case value='share'/>
                      <b:include cond='data:post.shareUrl' name='postShareButtons'/>
                      <b:case value='comments'/>
                      <b:include cond='data:post.allowComments' name='postCommentsLink'/>
                      <b:case value='location'/>
                      <b:include cond='data:post.location' name='postLocation'/>
                      <b:case value='timestamp'/>
                      <b:include cond='not data:view.isPage' name='postTimestamp'/>
                      <b:case value='author'/>
                      <b:include name='postAuthor'/>
                      <b:case value='labels'/>
                      <b:include cond='data:post.labels' name='postLabels'/>
                      <b:case value='icons'/>
                      <b:include cond='data:post.emailPostUrl' name='emailPostIcon'/>
                    </b:switch>
                  </b:includable>
                  <b:includable id='bylineRegion' var='regionItems'>
                    <b:loop values='data:regionItems' var='byline'>
                      <b:include data='byline' name='bylineByName'/>
                    </b:loop>
                  </b:includable>
                  <b:includable id='commentsLink'>
                    <a class='comment-link' expr:href='data:post.commentsUrl' expr:onclick='data:post.commentsUrlOnclick'>
                      <b:if cond='data:post.numberOfComments &gt; 0'>
                        <b:message name='messages.numberOfComments'>
                          <b:param expr:value='data:post.numberOfComments' name='numComments'/>
                        </b:message>
                        <b:else/>
                        <data:messages.postAComment/>
                      </b:if>
                    </a>
                  </b:includable>
                  <b:includable id='commentsLinkIframe'>
                    <!-- G+ comments, no longer available. The includable is retained for backwards-compatibility. -->
                  </b:includable>
                  <b:includable id='emailPostIcon'>
                    <span class='byline post-icons'>
                      <!-- email post links -->
                      <span class='item-action'>
                        <a expr:href='data:post.emailPostUrl' expr:title='data:messages.emailPost'>
                          <b:include data='{ iconClass: "touch-icon sharing-icon" }' name='emailIcon'/>
                        </a>
                      </span>
                    </span>
                  </b:includable>
                  <b:includable id='facebookShare'>
                    <b:with value='"window.open(this.href, \"_blank\", \"height=430,width=640\"); return false;"' var='onclick'>
                      <b:include name='platformShare'/>
                    </b:with>
                  </b:includable>
                  <b:includable id='footerBylines'>
                    <b:if cond='data:widgets.Blog.first.footerBylines'>
                      <b:loop index='i' values='data:widgets.Blog.first.footerBylines' var='region'>
                        <b:if cond='not data:region.items.empty'>
                          <div expr:class='"post-footer-line post-footer-line-" + (data:i + 1)'>
                            <b:with value='"footer-" + (data:i + 1)' var='regionName'>
                              <b:include data='region.items' name='bylineRegion'/>
                            </b:with>
                          </div>
                        </b:if>
                      </b:loop>
                    </b:if>
                  </b:includable>
                  <b:includable id='googlePlusShare'></b:includable>
                  <b:includable id='headerByline'>
                    <b:if cond='data:widgets.Blog.first.headerByline'>
                      <div class='post-header'>
                        <div class='post-header-line-1'>
                          <b:with value='"header-1"' var='regionName'>
                            <b:include data='data:widgets.Blog.first.headerByline.items' name='bylineRegion'/>
                          </b:with>
                        </div>
                      </div>
                    </b:if>
                  </b:includable>
                  <b:includable id='linkShare'>
                    <b:with value='"window.prompt(\"Copy to clipboard: Ctrl+C, Enter\", \"" + data:originalUrl + "\"); return false;"' var='onclick'>
                      <b:include name='platformShare'/>
                    </b:with>
                  </b:includable>
                  <b:includable id='otherSharingButton'>
                    <span class='sharing-platform-button sharing-element-other' expr:aria-label='data:messages.shareToOtherApps.escaped' expr:data-url='data:originalUrl' expr:title='data:messages.shareToOtherApps.escaped' role='menuitem' tabindex='-1'>
                      <b:with value='{key: "sharingOther"}' var='platform'>
                        <b:include name='sharingPlatformIcon'/>
                      </b:with>
                      <span class='platform-sharing-text'><data:messages.shareOtherApps.escaped/></span>
                    </span>
                  </b:includable>
                  <b:includable id='platformShare'>
                    <a expr:class='"goog-inline-block sharing-" + data:platform.key' expr:data-url='data:originalUrl' expr:href='data:shareUrl + "&amp;target=" + data:platform.target' expr:onclick='data:onclick ? data:onclick : ""' expr:title='data:platform.shareMessage' target='_blank'>
                      <span class='share-button-link-text'>
                        <data:platform.shareMessage/>
                      </span>
                    </a>
                  </b:includable>
                  <b:includable id='postAuthor'>
                    <span class='byline post-author vcard'>
                      <span class='post-author-label'>
                        <data:byline.label/>
                      </span>
                      <span class='fn'>
                        <b:if cond='data:post.author.profileUrl'>
                          <meta expr:content='data:post.author.profileUrl'/>
                          <a class='g-profile' expr:href='data:post.author.profileUrl' rel='author' title='author profile'>
                            <span><data:post.author.name/></span>
                          </a>
                          <b:else/>
                          <span><data:post.author.name/></span>
                        </b:if>
                      </span>
                    </span>
                  </b:includable>
                  <b:includable id='postCommentsLink'>
                    <span class='byline post-comment-link container'>
                      <b:include cond='data:post.commentSource != 1' name='commentsLink'/>
                    </span>
                  </b:includable>
                  <b:includable id='postJumpLink' var='post'>
                    <div class='jump-link flat-button'>
                      <a expr:href='data:post.url fragment "more"' expr:title='data:post.title'>
                        <b:eval expr='data:blog.jumpLinkMessage'/>
                      </a>
                    </div>
                  </b:includable>
                  <b:includable id='postLabels'>
                    <span class='byline post-labels'>
                      <span class='byline-label'><data:byline.label/></span>
                      <b:loop index='i' values='data:post.labels' var='label'>
                        <a expr:href='data:label.url' rel='tag'>
                          <data:label.name/>
                        </a>
                      </b:loop>
                    </span>
                  </b:includable>
                  <b:includable id='postLocation'>
                    <span class='byline post-location'>
                      <data:byline.label/>
                      <a expr:href='data:post.location.mapsUrl' target='_blank'><data:post.location.name/></a>
                    </span>
                  </b:includable>
                  <b:includable id='postReactions'>
                    <!-- Reaction feature no longer available. The includable is retained for backwards-compatibility. -->
                  </b:includable>
                  <b:includable id='postShareButtons'>
                    <div class='byline post-share-buttons goog-inline-block'>
                      <b:with value='data:sharingId ?: ((data:widget.instanceId ?: "sharing") + "-" + (data:regionName ?: "byline") + "-" + data:post.id)' var='sharingId'>
                        <!-- Note: 'sharingButtons' includable is from the default Sharing widget markup. -->
                        <b:include data='{                                                sharingId: data:sharingId,                                                originalUrl: data:post.url,                                                platforms: data:this.sharing.platforms,                                                shareUrl: data:post.shareUrl,                                                shareTitle: data:post.title,                                              }' name='sharingButtons'/>
                      </b:with>
                    </div>
                  </b:includable>
                  <b:includable id='postTimestamp'>
                    <span class='byline post-timestamp'>
                      <data:byline.label/>
                      <b:if cond='data:post.url'>
                        <meta expr:content='data:post.url.canonical'/>
                        <a class='timestamp-link' expr:href='data:post.url' rel='bookmark' title='permanent link'>
                          <time class='published' expr:datetime='data:post.date.iso8601' expr:title='data:post.date.iso8601'>
                            <data:post.date/>
                          </time>
                        </a>
                      </b:if>
                    </span>
                  </b:includable>
                  <b:includable id='sharingButton'>
                    <span expr:aria-label='data:platform.shareMessage' expr:class='"sharing-platform-button sharing-element-" + data:platform.key' expr:data-href='data:shareUrl + "&amp;target=" + data:platform.target' expr:data-url='data:originalUrl' expr:title='data:platform.shareMessage' role='menuitem' tabindex='-1'>
                      <b:include name='sharingPlatformIcon'/>
                      <span class='platform-sharing-text'><data:platform.name/></span>
                    </span>
                  </b:includable>
                  <b:includable id='sharingButtonContent'>
                    <div class='flat-icon-button ripple'>
                      <b:include name='shareIcon'/>
                    </div>
                  </b:includable>
                  <b:includable id='sharingButtons'>
                    <div class='sharing' expr:aria-owns='"sharing-popup-" + data:sharingId' expr:data-title='data:shareTitle'>
                      <button class='sharing-button touch-icon-button' expr:aria-controls='"sharing-popup-" + data:sharingId' expr:aria-label='data:messages.share.escaped' expr:id='"sharing-button-" + data:sharingId' role='button'>
                        <b:include name='sharingButtonContent'/>
                      </button>
                      <b:include name='sharingButtonsMenu'/>
                    </div>
                  </b:includable>
                  <b:includable id='sharingButtonsMenu'>
                    <div class='share-buttons-container'>
                      <ul aria-hidden='true' class='share-buttons hidden' expr:aria-label='data:messages.share.escaped' expr:id='"sharing-popup-" + data:sharingId' role='menu'>
                        <b:loop values='(data:platforms ?: data:blog.sharing.platforms) filter (p =&gt; p.key not in {"blogThis"})' var='platform'>
                          <li>
                            <b:include name='sharingButton'/>
                          </li>
                        </b:loop>
                        <li aria-hidden='true' class='hidden'>
                          <b:include name='otherSharingButton'/>
                        </li>
                      </ul>
                    </div>
                  </b:includable>
                  <b:includable id='sharingPlatformIcon'>
                    <b:include data='{ iconClass: ("touch-icon sharing-" + data:platform.key) }' expr:name='data:platform.key + "Icon"'/>
                  </b:includable>
                  <b:includable id='snippetedPostByline'>
                    <b:with value='(data:widgets first (w =&gt; w.type == "Blog")).allBylineItems' var='blogBylines'>
                      <div class='item-byline'>
                        <b:with value='data:blogBylines first (i =&gt; i.name == "author")' var='byline'>
                          <b:include cond='data:byline and data:this.postDisplay.showAuthor' data='post' name='postAuthor'/>
                        </b:with>
                        <b:with value='data:blogBylines first (i =&gt; i.name == "timestamp")' var='byline'>
                          <b:include cond='data:byline and data:this.postDisplay.showDate' data='post' name='postTimestamp'/>
                        </b:with>
                      </div>
                    </b:with>
                  </b:includable>
                  <b:includable id='snippetedPostContent'><b:if cond='data:widget.type == "PopularPosts"'><time class='post-date'><i class='fa fa-calendar-times-o'/><b:eval expr='format(data:post.date, "dd MMMM YYYY")'/></time></b:if><b:if cond='data:postDisplay.showFeaturedImage'><a class='item-thumbnail PLHolder' expr:href='data:post.url' expr:title="data:post.title.escaped"><b:if cond='data:post.featuredImage'><b:if cond='data:widget.type == "PopularPosts"'><img expr:alt='data:messages.image' expr:data-src='resizeImage(data:post.featuredImage, 72, "1:1")' width='72' height='72'/><b:elseif cond='data:widget.type == "FeaturedPost"'/><img expr:alt='data:messages.image' expr:data-src='resizeImage(data:post.featuredImage, 300, "3:2")' width='300' height='150'/></b:if><b:else/>&lt;img data-src='<b:include name='altImage'/>' alt='<data:messages.image/>'/&gt;</b:if></a></b:if><b:if cond='data:postDisplay.showTitle'><h3 class='post-title'><a expr:href='data:post.url'><data:post.title/></a></h3></b:if><b:if cond='data:widget.type == "FeaturedPost"'><div class='details'><b:if cond='data:widgets.Blog.first.allBylineItems.author'><div class='post-author'><b:if cond='data:post.author.profileUrl'><a expr:href='data:post.author.profileUrl' rel='nofollow noreferrer'><i class='fa fa-user-circle'/><data:post.author.name/></a><b:else/><i class='fa fa-user-circle'/><data:post.author.name/></b:if></div></b:if><b:if cond='data:widgets.Blog.first.allBylineItems.timestamp'><time class='post-date'><i class='fa fa-calendar-times-o'/><b:eval expr='format(data:post.date, "dd MMMM YYYY")'/></time></b:if></div></b:if><b:if cond='data:postDisplay.showSnippet'><p class='snippet-item'><b:if cond='data:widget.type == "PopularPosts"'><b:eval expr='data:post.snippets.long snippet { length: 120, links: false, linebreaks: false }'/><b:elseif cond='data:widget.type == "FeaturedPost"'/><b:eval expr='data:post.snippets.long snippet { length: 240, links: false, linebreaks: false }'/></b:if></p></b:if></b:includable>
                  <b:includable id='snippetedPostThumbnail'>
                    <div class='item-thumbnail'>
                      <a expr:href='data:post.url'>
                        <b:include data='{                         image: data:post.featuredImage,                         imageSizes: [72, 144],                         imageRatio: "1:1",                         sourceSizes: "72px"                        }' name='responsiveImage'/>
                      </a>
                    </div>
                  </b:includable>
                  <b:includable id='snippetedPostTitle'>
                    <b:if cond='data:post.title != ""'>
                      <h3 class='post-title'><a expr:href='data:post.url'><data:post.title/></a></h3>
                    </b:if>
                  </b:includable>
                  <b:includable id='snippetedPosts'>
                    <div role='feed'>
                      <!-- Don't render the post that we're currently already looking at. -->
                      <b:loop values='data:posts filter (p =&gt; p.id != data:view.postId)' var='post'>
                        <article class='post' expr:aria-labelledby='data:post.title.escaped'>
                          <b:include name='snippetedPostContent'/>
                        </article>
                      </b:loop>
                    </div>
                  </b:includable>
                </b:widget>
                <b:widget id='LinkList7' locked='false' title='سؤال وجواب [ACC]' type='LinkList' version='2' visible='true'>
                  <b:widget-settings>
                    <b:widget-setting name='sorting'>NONE</b:widget-setting>
                    <b:widget-setting name='text-1'>سؤال رقم 2</b:widget-setting>
                    <b:widget-setting name='link-1'>إجابة السؤال إجابة السؤال إجابة السؤال إجابة السؤال إجابة السؤال إجابة السؤال إجابة السؤال إجابة السؤال إجابة السؤال </b:widget-setting>
                    <b:widget-setting name='text-0'>سؤال رقم 1</b:widget-setting>
                    <b:widget-setting name='link-2'>إجابة السؤال إجابة السؤال إجابة السؤال إجابة السؤال إجابة السؤال إجابة السؤال </b:widget-setting>
                    <b:widget-setting name='link-0'>إجابة السؤال إجابة السؤال إجابة السؤال إجابة السؤال إجابة السؤال إجابة السؤال إجابة السؤال </b:widget-setting>
                    <b:widget-setting name='text-2'>سؤال رقم 3</b:widget-setting>
                  </b:widget-settings>
                  <b:includable id='main'><b:include name='widget-title'/><b:include name='content'/></b:includable>
                  <b:includable id='ACC'><div class='widget-content accordion-widget'><b:loop index='index' values='data:links' var='link'><div expr:class='data:index == 0 ? "opened acc-head" : "acc-head"'><data:link.name/></div><div class='acc-body notr'><data:link.target/></div></b:loop></div></b:includable>
                  <b:includable id='AUTH'><b:if cond='data:widget.instanceId == "LinkList500"'><b:tag name='script' type='text/javascript'><b:loop values='data:links' var='link'>AuthorsInfo['<data:link.name/>']='<data:link.target.jsEscaped/>';</b:loop></b:tag><b:else/><b:tag name='script' type='text/javascript'>(function(){var snapAuthor=AuthorsInfo.filter(function(a){return a.name==='<data:title/>'})[0];if(snapAuthor!==undefined){snapAuthor.provided=true;<b:loop values='data:links' var='link'><b:if cond='data:link.name contains "-ad"'>snapAuthor['<data:link.name/>']='<data:link.target.jsEscaped/>';<b:else/><b:switch var='data:link.name'><b:case value='rank'/>snapAuthor.rank='<data:link.target.escaped/>';<b:case value='about'/>snapAuthor.about='<data:link.target.escaped/>';<b:default/>snapAuthor.links['<data:link.name/>']='<data:link.target/>';</b:switch></b:if></b:loop>}})();</b:tag></b:if></b:includable>
                  <b:includable id='DEF'><nav class='widget-content'><ul><b:loop values='data:links' var='link'><li><a expr:href='data:link.target' expr:title='data:link.name'><data:link.name/></a></li></b:loop></ul></nav></b:includable>
                  <b:includable id='GAL'><div class='gallery-widget'><b:loop values='data:links' var='link'><a expr:href='data:link.name' target='_blank'><img expr:alt='data:link.name' expr:src='data:link.target' expr:title='data:link.name'/></a></b:loop></div></b:includable>
                  <b:includable id='SOC'><ul class='social-widget social'><b:loop values='data:links' var='link'><li><a expr:href='data:link.target' expr:title='data:link.name' rel='nofollow noreferrer' target='_blank'><data:link.name/></a></li></b:loop></ul></b:includable>
                  <b:includable id='content'>
                    <b:if cond='data:title contains "[ACC]"'><b:include name='ACC'/>
                      <b:elseif cond='data:title contains "[SOC]"'/><b:include name='SOC'/>
                      <b:elseif cond='data:title contains "[GAL]"'/><b:include name='GAL'/>
                      <b:elseif cond='data:widget.sectionId == "Auth-Sec"'/><b:include name='AUTH'/>
                      <b:else/><b:include name='DEF'/>
                    </b:if>
                  </b:includable>
                </b:widget>
                <b:widget id='HTML30' locked='false' title='المشاركات الأخيرة' type='HTML' version='2' visible='true'>
                  <b:widget-settings>
                    <b:widget-setting name='content'><![CDATA[<i class="sqWid" data-type="thumbs" data-len="5" data-label="recent"></i>]]></b:widget-setting>
                  </b:widget-settings>
                  <b:includable id='main'><b:include name='widget-title'/><div class='widget-content'><data:content/></div></b:includable>
                </b:widget>
                <b:widget id='HTML40' locked='false' title='التعليقات' type='HTML' version='2' visible='true'>
                  <b:widget-settings>
                    <b:widget-setting name='content'><![CDATA[<i class="sqWid" data-type="comments" data-len="5" data-label="comments"></i>]]></b:widget-setting>
                  </b:widget-settings>
                  <b:includable id='main'><b:include name='widget-title'/><div class='widget-content'><data:content/></div></b:includable>
                </b:widget>
              </b:section>
            </aside>
          </b:if><!-- End IF Label Page & Not Error Page -->
          <i class='clear'/>




        </div> <!-- end middle-content -->


        <!-- Homepage Sections -->
        <div class='bottom-content wide-sec'>
          <b:if cond='data:view.isHomepage'>
            <div class='sided-sections cate'>
              <b:section id='section16'/>
              <b:section id='section17'/>
              <b:section id='section18'/>
              <i class='clear'/>
            </div>
            <div class='sided-sections cate opt-before'>
              <b:section id='section19'/>
              <b:section id='section20'/>
              <b:section id='section21'/>
              <i class='clear'/>
            </div>
          </b:if>
        </div>




        <i class='clear'/>
      </div> <!-- End main-wrap -->
      <i class='clear'/>




      <footer id='footer'>
        <b:section class='wrapper' id='footer-top-section'>
          <b:widget id='Label3' locked='false' title='التسميات' type='Label' visible='true'>
            <b:widget-settings>
              <b:widget-setting name='sorting'>ALPHA</b:widget-setting>
              <b:widget-setting name='display'>CLOUD</b:widget-setting>
              <b:widget-setting name='selectedLabelsList'/>
              <b:widget-setting name='showType'>ALL</b:widget-setting>
              <b:widget-setting name='showFreqNumbers'>true</b:widget-setting>
            </b:widget-settings>
            <b:includable id='main' var='this'><b:include name='widget-title'/><b:include name='content'/></b:includable>
            <b:includable id='cloud'><div expr:class='data:this.display + "-label-widget-content"'><b:loop values='data:labels' var='label'><span class='label-size'><a class='label-name' expr:href='data:label.url' expr:title='data:label.name'><data:label.name/></a></span></b:loop></div></b:includable>
            <b:includable id='content'><b:include cond='data:this.display == "list"' name='list'/><b:include cond='data:this.display == "cloud"' name='cloud'/></b:includable>
            <b:includable id='list'><nav expr:aria-label='data:title' expr:class='data:this.display + "-label-widget-content"'><ul><b:loop values='data:labels' var='label'><li><a class='label-name' expr:href='data:label.url' expr:title='data:label.name'><data:label.name/></a><b:if cond='data:this.showFreqNumbers'><span class='label-count'><data:label.count/></span></b:if></li></b:loop></ul></nav></b:includable>
          </b:widget>
        </b:section>
        <div class='wrapper' id='footer-sections'>
          <b:section class='f-sec' id='sec1'>
            <b:widget id='Label2' locked='false' title='التسميات' type='Label' visible='true'>
              <b:widget-settings>
                <b:widget-setting name='sorting'>ALPHA</b:widget-setting>
                <b:widget-setting name='display'>LIST</b:widget-setting>
                <b:widget-setting name='selectedLabelsList'/>
                <b:widget-setting name='showType'>ALL</b:widget-setting>
                <b:widget-setting name='showFreqNumbers'>true</b:widget-setting>
              </b:widget-settings>
              <b:includable id='main' var='this'><b:include name='widget-title'/><b:include name='content'/></b:includable>
              <b:includable id='cloud'><div expr:class='data:this.display + "-label-widget-content"'><b:loop values='data:labels' var='label'><span class='label-size'><a class='label-name' expr:href='data:label.url' expr:title='data:label.name'><data:label.name/></a></span></b:loop></div></b:includable>
              <b:includable id='content'><b:include cond='data:this.display == "list"' name='list'/><b:include cond='data:this.display == "cloud"' name='cloud'/></b:includable>
              <b:includable id='list'><nav expr:aria-label='data:title' expr:class='data:this.display + "-label-widget-content"'><ul><b:loop values='data:labels' var='label'><li><a class='label-name' expr:href='data:label.url' expr:title='data:label.name'><data:label.name/></a><b:if cond='data:this.showFreqNumbers'><span class='label-count'><data:label.count/></span></b:if></li></b:loop></ul></nav></b:includable>
            </b:widget>
          </b:section>
          <b:section class='f-sec' id='sec2'>
            <b:widget id='FeaturedPost1' locked='false' title='مشاركة مميزة' type='FeaturedPost' version='2' visible='true'>
              <b:widget-settings>
                <b:widget-setting name='showSnippet'>false</b:widget-setting>
                <b:widget-setting name='showPostTitle'>true</b:widget-setting>
                <b:widget-setting name='postId'>0</b:widget-setting>
                <b:widget-setting name='showFirstImage'>true</b:widget-setting>
                <b:widget-setting name='useMostRecentPost'>true</b:widget-setting>
              </b:widget-settings>
              <b:includable id='main' var='this'><b:include name='widget-title'/><div class='widget-content'><b:include name='snippetedPosts'/></div></b:includable>
              <b:includable id='blogThisShare'>
                <b:with value='"window.open(this.href, \"_blank\", \"height=270,width=475\"); return false;"' var='onclick'>
                  <b:include name='platformShare'/>
                </b:with>
              </b:includable>
              <b:includable id='bylineByName' var='byline'>
                <b:switch var='data:byline.name'>
                  <b:case value='share'/>
                  <b:include cond='data:post.shareUrl' name='postShareButtons'/>
                  <b:case value='comments'/>
                  <b:include cond='data:post.allowComments' name='postCommentsLink'/>
                  <b:case value='location'/>
                  <b:include cond='data:post.location' name='postLocation'/>
                  <b:case value='timestamp'/>
                  <b:include cond='not data:view.isPage' name='postTimestamp'/>
                  <b:case value='author'/>
                  <b:include name='postAuthor'/>
                  <b:case value='labels'/>
                  <b:include cond='data:post.labels' name='postLabels'/>
                  <b:case value='icons'/>
                  <b:include cond='data:post.emailPostUrl' name='emailPostIcon'/>
                </b:switch>
              </b:includable>
              <b:includable id='bylineRegion' var='regionItems'>
                <b:loop values='data:regionItems' var='byline'>
                  <b:include data='byline' name='bylineByName'/>
                </b:loop>
              </b:includable>
              <b:includable id='commentsLink'>
                <a class='comment-link' expr:href='data:post.commentsUrl' expr:onclick='data:post.commentsUrlOnclick'>
                  <b:if cond='data:post.numberOfComments &gt; 0'>
                    <b:message name='messages.numberOfComments'>
                      <b:param expr:value='data:post.numberOfComments' name='numComments'/>
                    </b:message>
                    <b:else/>
                    <data:messages.postAComment/>
                  </b:if>
                </a>
              </b:includable>
              <b:includable id='commentsLinkIframe'>
                <!-- G+ comments, no longer available. The includable is retained for backwards-compatibility. -->
              </b:includable>
              <b:includable id='emailPostIcon'>
                <span class='byline post-icons'>
                  <!-- email post links -->
                  <span class='item-action'>
                    <a expr:href='data:post.emailPostUrl' expr:title='data:messages.emailPost'>
                      <b:include data='{ iconClass: "touch-icon sharing-icon" }' name='emailIcon'/>
                    </a>
                  </span>
                </span>
              </b:includable>
              <b:includable id='facebookShare'>
                <b:with value='"window.open(this.href, \"_blank\", \"height=430,width=640\"); return false;"' var='onclick'>
                  <b:include name='platformShare'/>
                </b:with>
              </b:includable>
              <b:includable id='footerBylines'>
                <b:if cond='data:widgets.Blog.first.footerBylines'>
                  <b:loop index='i' values='data:widgets.Blog.first.footerBylines' var='region'>
                    <b:if cond='not data:region.items.empty'>
                      <div expr:class='"post-footer-line post-footer-line-" + (data:i + 1)'>
                        <b:with value='"footer-" + (data:i + 1)' var='regionName'>
                          <b:include data='region.items' name='bylineRegion'/>
                        </b:with>
                      </div>
                    </b:if>
                  </b:loop>
                </b:if>
              </b:includable>
              <b:includable id='googlePlusShare'></b:includable>
              <b:includable id='headerByline'>
                <b:if cond='data:widgets.Blog.first.headerByline'>
                  <div class='post-header'>
                    <div class='post-header-line-1'>
                      <b:with value='"header-1"' var='regionName'>
                        <b:include data='data:widgets.Blog.first.headerByline.items' name='bylineRegion'/>
                      </b:with>
                    </div>
                  </div>
                </b:if>
              </b:includable>
              <b:includable id='linkShare'>
                <b:with value='"window.prompt(\"Copy to clipboard: Ctrl+C, Enter\", \"" + data:originalUrl + "\"); return false;"' var='onclick'>
                  <b:include name='platformShare'/>
                </b:with>
              </b:includable>
              <b:includable id='otherSharingButton'>
                <span class='sharing-platform-button sharing-element-other' expr:aria-label='data:messages.shareToOtherApps.escaped' expr:data-url='data:originalUrl' expr:title='data:messages.shareToOtherApps.escaped' role='menuitem' tabindex='-1'>
                  <b:with value='{key: "sharingOther"}' var='platform'>
                    <b:include name='sharingPlatformIcon'/>
                  </b:with>
                  <span class='platform-sharing-text'><data:messages.shareOtherApps.escaped/></span>
                </span>
              </b:includable>
              <b:includable id='platformShare'>
                <a expr:class='"goog-inline-block sharing-" + data:platform.key' expr:data-url='data:originalUrl' expr:href='data:shareUrl + "&amp;target=" + data:platform.target' expr:onclick='data:onclick ? data:onclick : ""' expr:title='data:platform.shareMessage' target='_blank'>
                  <span class='share-button-link-text'>
                    <data:platform.shareMessage/>
                  </span>
                </a>
              </b:includable>
              <b:includable id='postAuthor'>
                <span class='byline post-author vcard'>
                  <span class='post-author-label'>
                    <data:byline.label/>
                  </span>
                  <span class='fn'>
                    <b:if cond='data:post.author.profileUrl'>
                      <meta expr:content='data:post.author.profileUrl'/>
                      <a class='g-profile' expr:href='data:post.author.profileUrl' rel='author' title='author profile'>
                        <span><data:post.author.name/></span>
                      </a>
                      <b:else/>
                      <span><data:post.author.name/></span>
                    </b:if>
                  </span>
                </span>
              </b:includable>
              <b:includable id='postCommentsLink'>
                <span class='byline post-comment-link container'>
                  <b:include cond='data:post.commentSource != 1' name='commentsLink'/>
                </span>
              </b:includable>
              <b:includable id='postJumpLink' var='post'>
                <div class='jump-link flat-button'>
                  <a expr:href='data:post.url fragment "more"' expr:title='data:post.title'>
                    <b:eval expr='data:blog.jumpLinkMessage'/>
                  </a>
                </div>
              </b:includable>
              <b:includable id='postLabels'>
                <span class='byline post-labels'>
                  <span class='byline-label'><data:byline.label/></span>
                  <b:loop index='i' values='data:post.labels' var='label'>
                    <a expr:href='data:label.url' rel='tag'>
                      <data:label.name/>
                    </a>
                  </b:loop>
                </span>
              </b:includable>
              <b:includable id='postLocation'>
                <span class='byline post-location'>
                  <data:byline.label/>
                  <a expr:href='data:post.location.mapsUrl' target='_blank'><data:post.location.name/></a>
                </span>
              </b:includable>
              <b:includable id='postReactions'>
                <!-- Reaction feature no longer available. The includable is retained for backwards-compatibility. -->
              </b:includable>
              <b:includable id='postShareButtons'>
                <div class='byline post-share-buttons goog-inline-block'>
                  <b:with value='data:sharingId ?: ((data:widget.instanceId ?: "sharing") + "-" + (data:regionName ?: "byline") + "-" + data:post.id)' var='sharingId'>
                    <!-- Note: 'sharingButtons' includable is from the default Sharing widget markup. -->
                    <b:include data='{                                                sharingId: data:sharingId,                                                originalUrl: data:post.url,                                                platforms: data:this.sharing.platforms,                                                shareUrl: data:post.shareUrl,                                                shareTitle: data:post.title,                                              }' name='sharingButtons'/>
                  </b:with>
                </div>
              </b:includable>
              <b:includable id='postTimestamp'>
                <span class='byline post-timestamp'>
                  <data:byline.label/>
                  <b:if cond='data:post.url'>
                    <meta expr:content='data:post.url.canonical'/>
                    <a class='timestamp-link' expr:href='data:post.url' rel='bookmark' title='permanent link'>
                      <time class='published' expr:datetime='data:post.date.iso8601' expr:title='data:post.date.iso8601'>
                        <data:post.date/>
                      </time>
                    </a>
                  </b:if>
                </span>
              </b:includable>
              <b:includable id='sharingButton'>
                <span expr:aria-label='data:platform.shareMessage' expr:class='"sharing-platform-button sharing-element-" + data:platform.key' expr:data-href='data:shareUrl + "&amp;target=" + data:platform.target' expr:data-url='data:originalUrl' expr:title='data:platform.shareMessage' role='menuitem' tabindex='-1'>
                  <b:include name='sharingPlatformIcon'/>
                  <span class='platform-sharing-text'><data:platform.name/></span>
                </span>
              </b:includable>
              <b:includable id='sharingButtonContent'>
                <div class='flat-icon-button ripple'>
                  <b:include name='shareIcon'/>
                </div>
              </b:includable>
              <b:includable id='sharingButtons'>
                <div class='sharing' expr:aria-owns='"sharing-popup-" + data:sharingId' expr:data-title='data:shareTitle'>
                  <button class='sharing-button touch-icon-button' expr:aria-controls='"sharing-popup-" + data:sharingId' expr:aria-label='data:messages.share.escaped' expr:id='"sharing-button-" + data:sharingId' role='button'>
                    <b:include name='sharingButtonContent'/>
                  </button>
                  <b:include name='sharingButtonsMenu'/>
                </div>
              </b:includable>
              <b:includable id='sharingButtonsMenu'>
                <div class='share-buttons-container'>
                  <ul aria-hidden='true' class='share-buttons hidden' expr:aria-label='data:messages.share.escaped' expr:id='"sharing-popup-" + data:sharingId' role='menu'>
                    <b:loop values='(data:platforms ?: data:blog.sharing.platforms) filter (p =&gt; p.key not in {"blogThis"})' var='platform'>
                      <li>
                        <b:include name='sharingButton'/>
                      </li>
                    </b:loop>
                    <li aria-hidden='true' class='hidden'>
                      <b:include name='otherSharingButton'/>
                    </li>
                  </ul>
                </div>
              </b:includable>
              <b:includable id='sharingPlatformIcon'>
                <b:include data='{ iconClass: ("touch-icon sharing-" + data:platform.key) }' expr:name='data:platform.key + "Icon"'/>
              </b:includable>
              <b:includable id='snippetedPostByline'>
                <b:with value='(data:widgets first (w =&gt; w.type == "Blog")).allBylineItems' var='blogBylines'>
                  <div class='item-byline'>
                    <b:with value='data:blogBylines first (i =&gt; i.name == "author")' var='byline'>
                      <b:include cond='data:byline and data:this.postDisplay.showAuthor' data='post' name='postAuthor'/>
                    </b:with>
                    <b:with value='data:blogBylines first (i =&gt; i.name == "timestamp")' var='byline'>
                      <b:include cond='data:byline and data:this.postDisplay.showDate' data='post' name='postTimestamp'/>
                    </b:with>
                  </div>
                </b:with>
              </b:includable>
              <b:includable id='snippetedPostContent'><b:if cond='data:widget.type == "PopularPosts"'><time class='post-date'><i class='fa fa-calendar-times-o'/><b:eval expr='format(data:post.date, "dd MMMM YYYY")'/></time></b:if><b:if cond='data:postDisplay.showFeaturedImage'><a class='item-thumbnail PLHolder' expr:href='data:post.url' expr:title="data:post.title.escaped"><b:if cond='data:post.featuredImage'><b:if cond='data:widget.type == "PopularPosts"'><img expr:alt='data:messages.image' expr:data-src='resizeImage(data:post.featuredImage, 72, "1:1")' width='72' height='72'/><b:elseif cond='data:widget.type == "FeaturedPost"'/><img expr:alt='data:messages.image' expr:data-src='resizeImage(data:post.featuredImage, 300, "3:2")' width='300' height='150'/></b:if><b:else/>&lt;img data-src='<b:include name='altImage'/>' alt='<data:messages.image/>'/&gt;</b:if></a></b:if><b:if cond='data:postDisplay.showTitle'><h3 class='post-title'><a expr:href='data:post.url'><data:post.title/></a></h3></b:if><b:if cond='data:widget.type == "FeaturedPost"'><div class='details'><b:if cond='data:widgets.Blog.first.allBylineItems.author'><div class='post-author'><b:if cond='data:post.author.profileUrl'><a expr:href='data:post.author.profileUrl' rel='nofollow noreferrer'><i class='fa fa-user-circle'/><data:post.author.name/></a><b:else/><i class='fa fa-user-circle'/><data:post.author.name/></b:if></div></b:if><b:if cond='data:widgets.Blog.first.allBylineItems.timestamp'><time class='post-date'><i class='fa fa-calendar-times-o'/><b:eval expr='format(data:post.date, "dd MMMM YYYY")'/></time></b:if></div></b:if><b:if cond='data:postDisplay.showSnippet'><p class='snippet-item'><b:if cond='data:widget.type == "PopularPosts"'><b:eval expr='data:post.snippets.long snippet { length: 120, links: false, linebreaks: false }'/><b:elseif cond='data:widget.type == "FeaturedPost"'/><b:eval expr='data:post.snippets.long snippet { length: 240, links: false, linebreaks: false }'/></b:if></p></b:if></b:includable>
              <b:includable id='snippetedPostThumbnail'>
                <div class='item-thumbnail'>
                  <a expr:href='data:post.url'>
                    <b:include data='{                         image: data:post.featuredImage,                         imageSizes: [72, 144],                         imageRatio: "1:1",                         sourceSizes: "72px"                        }' name='responsiveImage'/>
                  </a>
                </div>
              </b:includable>
              <b:includable id='snippetedPostTitle'>
                <b:if cond='data:post.title != ""'>
                  <h3 class='post-title'><a expr:href='data:post.url'><data:post.title/></a></h3>
                </b:if>
              </b:includable>
              <b:includable id='snippetedPosts'>
                <div role='feed'>
                  <!-- Don't render the post that we're currently already looking at. -->
                  <b:loop values='data:posts filter (p =&gt; p.id != data:view.postId)' var='post'>
                    <article class='post' expr:aria-labelledby='data:post.title.escaped'>
                      <b:include name='snippetedPostContent'/>
                    </article>
                  </b:loop>
                </div>
              </b:includable>
            </b:widget>
          </b:section>
          <b:section class='f-sec' id='sec3'>
            <b:widget id='PageList1' locked='false' title='الصفحات' type='PageList' visible='true'>
              <b:includable id='main'><b:include name='widget-title'/><b:include name='content'/></b:includable>
              <b:includable id='content'><nav class='widget-content'><b:include name='pageList'/></nav></b:includable>
              <b:includable id='overflowButton'><b:include name='verticalMoreIcon'/></b:includable>
              <b:includable id='overflowablePageList'><div class='overflowable-container'><div class='overflowable-contents'><div class='container'><b:with value='true' var='overflow'><b:with value='"tabs"' var='pageListClass'><b:include name='pageList'/></b:with></b:with></div></div><div class='overflow-button hidden'><b:include name='overflowButton'/></div></div></b:includable>
              <b:includable id='pageLink'><li><b:class cond='data:overflow' name='overflowable-item'/><b:class cond='data:link.isCurrentPage' name='selected'/><a expr:href='data:link.href' expr:title='data:link.title'><data:link.title/></a></li></b:includable>
              <b:includable id='pageList'><nav><ul><b:class cond='data:pageListClass' expr:name='data:pageListClass'/><b:loop values='data:links' var='link'><b:include name='pageLink'/></b:loop></ul></nav></b:includable>
            </b:widget>
          </b:section>
          <b:section class='f-sec' id='sec4'/>
          <i class='clear'/>
        </div>
        <b:section class='wrapper' id='footer-bottom-section'/>
        <div class='color-wrap' role='contentinfo'>
          <button class='scroll-top' title='↑'><i class='fa fa-chevron-up'/></button>
          <b:section class='wrapper' id='footer-cop-section' maxwidgets='2' showaddelement='no'>
            <b:widget id='HTML303' locked='true' title='حقوق المدونة' type='HTML' version='2' visible='true'>
              <b:widget-settings>
                <b:widget-setting name='content'/>
              </b:widget-settings>
              <b:includable id='main'><span data-trans='13'/><b>&#169;</b><a expr:href='data:blog.homepageUrl'><data:blog.title/></a></b:includable>
            </b:widget>
            <b:widget id='LinkList304' locked='true' title='مواقع التواصل الإجتماعي' type='LinkList' version='2' visible='true'>
              <b:widget-settings>
                <b:widget-setting name='text-0'>facebook</b:widget-setting>
                <b:widget-setting name='link-0'>#</b:widget-setting>
                <b:widget-setting name='text-1'>twitter</b:widget-setting>
                <b:widget-setting name='link-1'>#</b:widget-setting>
                <b:widget-setting name='text-2'>youtube</b:widget-setting>
                <b:widget-setting name='link-2'>#</b:widget-setting>
                <b:widget-setting name='text-3'>tiktok</b:widget-setting>
                <b:widget-setting name='link-3'>#</b:widget-setting>
                <b:widget-setting name='text-4'>pinterest</b:widget-setting>
                <b:widget-setting name='link-4'>#</b:widget-setting>
                <b:widget-setting name='text-5'>rss</b:widget-setting>
                <b:widget-setting name='link-5'>#</b:widget-setting>
                <b:widget-setting name='sorting'>NONE</b:widget-setting>
              </b:widget-settings>
              <b:includable id='main'><b:include name='content'/></b:includable>
              <b:includable id='ACC'/>
              <b:includable id='AUTH'/>
              <b:includable id='DEF'/>
              <b:includable id='GAL'/>
              <b:includable id='SOC'/>
              <b:includable id='content'><nav expr:area-label='data:title'><ul class='social-static social'><b:loop values='data:links' var='link'><b:if cond='data:link.name in ["khamsat","mostaql","tradent","google-play","messenger","blogger","discord","tiktok","patreon"]'><li><a expr:href='data:link.target' expr:title='data:link.name' rel='nofollow noreferrer' target='_blank'><svg expr:class='"fa-" + data:link.name'><use expr:href='"#ic-"+data:link.name'/></svg></a></li><b:else/><li><a expr:href='data:link.target' expr:title='data:link.name' rel='nofollow noreferrer' target='_blank'><i expr:class='"fa fa-" + data:link.name'/></a></li></b:if></b:loop></ul></nav></b:includable>
            </b:widget>
            <b:widget id='HTML304' locked='true' title='أداة الترجمة' type='HTML' version='2' visible='true'>
              <b:widget-settings>
                <b:widget-setting name='content'/>
              </b:widget-settings>
              <b:includable id='main'><b:include name='content'/></b:includable>
              <b:includable id='content'><b:if cond='data:content != ""'><b:tag name='script' type='text/javascript'>var trans=[<data:content/>]</b:tag><b:else/><b:switch var='data:blog.locale'><b:case value='en'/><b:include name='en'/><b:case value='en-GB'/><b:include name='en'/><b:case value='fr'/><b:include name='fr'/><b:case value='fr-CA'/><b:include name='fr'/><b:case value='de'/><b:include name='de'/><b:default/><b:include name='default'/></b:switch></b:if></b:includable>
              <b:includable id='de'><b:tag name='script' type='text/javascript'>/*<![CDATA[*/var trans=['Geben Sie Ihre E-Mail-Adresse ein, um sofort zu neuen veröffentlichten Beiträgen zu gelangen','Zuhause','Nächster','Bisherige','Veröffentlichen','Empfehlen',"Tweet",'Sparen','Aktie','Senden','Drucken','Nur Kommentare anzeigen','Kommentare und Antworten anzeigen',"Alle Urheberrechte vorbehalten",'Antwort hinterlassen','Post','Bemerkungen','Premium-Inhalte','Beitrag teilen und über den geteilten Link zurückkehren, um den versteckten Inhalt anzuzeigen','Profil','Nächster Beitrag',"Vorheriger Beitrag",'Kommentar löschen','Antworten','Warten Sie mal..','Verknüpfung','zweite',"Sekunden",'zwei Sekunden','eine Sekunde','Link bereit','Siehe auch','[BILD]','[VIDEO]','Größe','Inhaltsverzeichnis','Siehe auch','Achtergrondmodus','Nachtmodus','Toevoegen aan toepassingen','Kommentare ein-/ausblenden']/*]]>*/</b:tag></b:includable>
              <b:includable id='default'><b:tag name='script' type='text/javascript'>/*<![CDATA[*/var trans=['أدخل بريدك الإلكتروني هنا لتشترك معنا ويصلك آخر وأهم الموضوعات على بريدك أولا بأول','الرئيسية','التالي','السابق','نشر','توصية','تغريد','حفظ','مشاركة','إرسال','طباعة','عرض التعليقات فقط','عرض التعليقات والردود','جميع الحقوق محفوظة','اترك رداً','مشاركة','تعليقات','المحتوى مخفي','قم بمشاركة الموضوع وأعد الدخول عبر الرابط المنشور لإظهار المحتوى','الملف الشخصي','الموضوع التالي','الموضوع السابق','حذف التعليق','رد على التعليق','برجاء الإنتظار يتم تحضير الرابط','رابط الصفحة','ثانية','ثواني','ثانيتان','ثانية واحدة','الرابط جاهز الآن','شاهد أيضاً','[صورة]','[فيديو]','الحجم','محتويات المقال','شاهد أيضًا', 'نمط الخلفية','الوضع الليلي','أضف للتطبيقات','إظهار / إخفاء التعليقات']/*]]>*/</b:tag></b:includable>
              <b:includable id='en'><b:tag name='script' type='text/javascript'>/*<![CDATA[*/var trans=['Enter your email address to reach new published posts immediately','Home','Next','Previous','Publish','Recommend','Tweet','Save','Share','Send','Print','Show Comments Only','Show Comments & Replies','All copyrights reserved','Leave Reply','Post','Comments','Premium Content','share post and return via shared link to view hidden content','Profile','Next Post','Prev Post','Delete Comment','Reply','Please Wait..','Link','second','seconds','two seconds','one second','Link ready','See Also','[IMAGE]','[VIDEO]','size','Table of Contents','See Also','Background Style','Night Mode','Add to Apps','Show / Hide comments']/*]]>*/</b:tag></b:includable>
              <b:includable id='fr'><b:tag name='script' type='text/javascript'>/*<![CDATA[*/var trans=['Entrez votre adresse email pour atteindre immédiatement les nouveaux articles publiés','Accueil','Suivant','Précédent','Publier','Recommander','Tweet','Sauvegarder','Partager','Envoyer','Impression','Afficher uniquement les commentaires','Afficher les commentaires et les réponses','Tous droits réservés','Laisser une réponse','Poster','Commentaires','Contenu de qualité','partager un message et le renvoyer via un lien partagé pour afficher le contenu masqué','Profil','Next Post','Prev Post','Supprimer le commentaire','Répondre','S\'il vous plaît, attendez..','Lien','seconde','secondes','deux secondes','une seconde','Link ready','Voir également','[IMAGE]','[VIDÉO]','Taille','Table des matières','Voir également','Mode d\'arrière-plan','Mode Nuit','Ajouter aux Apps','Afficher / Masquer les commentaires']/*]]>*/</b:tag></b:includable>
            </b:widget>
          </b:section>
        </div>
      </footer>
      <i class='clear'/>
    </div> <!-- End main-container -->

    <!-- Custom Icons -->
    <svg class='hide'><symbol id='ic-google-play' viewbox='0 0 28 28'><title>google-play</title><path d='M6.111 0.448c-1.095-0.605-2.382-0.591-3.47 0.009l12.757 11.768 4.286-4.286-13.572-7.491z'/><path d='M1.366 1.661c-0.311 0.509-0.49 1.092-0.49 1.71v21.254c0 0.598 0.163 1.169 0.457 1.668l12.829-12.829-12.795-11.803z'/><path d='M25.366 11.075l-4.088-2.256-4.593 4.592 5.629 5.192 3.054-1.685c1.099-0.609 1.757-1.701 1.757-2.922-0.002-1.221-0.658-2.313-1.759-2.921z'/><path d='M15.447 14.65l-12.864 12.864c0.558 0.318 1.171 0.486 1.787 0.486 0.595 0 1.193-0.151 1.741-0.453l14.589-8.051-5.253-4.845z'/></symbol><symbol id='ic-khamsat' viewbox='0 0 20 28'><title>khamsat</title><path d='M0.475-0.065h0.429c2.759-0.024 5.405-0.019 7.94 0.019v5.443h-3.355c0 0.41 0.004 0.979 0.009 1.706s0.009 1.301 0.009 1.724c-1.342 0.41-2.494 1.096-3.458 2.060s-1.644 2.084-2.041 3.364c-0.037-2.212-0.043-6.705-0.019-13.476v-0.391c0-0.037-0.004-0.084-0.009-0.14s-0.009-0.093-0.009-0.112v-0.075c0 0 0.009-0.019 0.028-0.056s0.037-0.050 0.056-0.037 0.050 0.004 0.093-0.028h0.326zM9.683-0.047c0.969 0 2.488-0.004 4.557-0.009s3.588-0.009 4.557-0.009c0.050 2.261 0.075 4.082 0.075 5.461h-9.189v-5.443zM8.844 8.733c0 0.472 0.006 1.45 0.019 2.936s0.006 2.246-0.019 2.283c-0.062 0.013-0.14 0.032-0.233 0.056s-0.274 0.093-0.541 0.205-0.518 0.242-0.755 0.391-0.494 0.36-0.774 0.634-0.509 0.572-0.69 0.895c-0.181 0.322-0.313 0.731-0.401 1.221s-0.093 1.016-0.019 1.575c-0.434 0.037-2.106 0.037-5.014 0-0.037-1.217 0.069-2.334 0.317-3.346s0.615-1.896 1.1-2.647 1.087-1.413 1.808-1.985c0.721-0.572 1.51-1.038 2.367-1.398s1.802-0.634 2.833-0.82zM10.093 27.968h-0.41v-5.443c1.366 0.41 2.548 0.149 3.541-0.783 0.796-0.746 1.286-1.733 1.473-2.964 0.186-1.292-0.032-2.41-0.652-3.355-0.783-1.206-2.032-1.776-3.747-1.715-0.199 0-0.404 0.013-0.615 0.037v-5.014c2.908-0.36 5.262 0.317 7.064 2.032 1.404 1.329 2.293 3.1 2.665 5.312 0.349 2.138 0.144 4.207-0.615 6.207-0.783 2.050-1.994 3.573-3.635 4.567-1.478 0.895-3.169 1.267-5.070 1.118zM0.885 22.544c0.796 0 2.121-0.006 3.979-0.019s3.184-0.019 3.98-0.019v5.48h-7.94c0-0.895-0.004-1.879-0.009-2.954s-0.009-1.905-0.009-2.488z'/></symbol><symbol id='ic-mostaql' viewbox='0 0 29 28'><title>mostaql</title><path d='M28.679 10.845c0.755 4.365 0.119 8.055-1.911 11.075-1.522 2.265-3.657 3.928-6.405 4.989-2.666 1.014-5.449 1.322-8.351 0.92-2.96-0.425-5.479-1.522-7.555-3.291-2.442-2.075-3.88-4.777-4.317-8.103-0.235-1.746-0.177-3.609 0.177-5.591 0.248-1.357 0.717-2.633 1.407-3.83s1.527-2.238 2.512-3.123 2.082-1.633 3.291-2.247 2.468-1.056 3.777-1.327c1.191-0.225 2.362-0.331 3.512-0.318s2.27 0.156 3.362 0.433 2.12 0.642 3.087 1.097 1.867 1.014 2.698 1.681 1.566 1.392 2.203 2.176 1.173 1.642 1.61 2.574 0.738 1.893 0.902 2.884zM14.499 21.656c1.067 0 2.088-0.207 3.061-0.619s1.813-0.973 2.521-1.681 1.27-1.548 1.69-2.521 0.628-1.996 0.628-3.070-0.209-2.097-0.628-3.070-0.982-1.813-1.69-2.521-1.548-1.269-2.521-1.681c-0.973-0.412-1.994-0.619-3.061-0.619s-2.091 0.207-3.070 0.619c-0.978 0.412-1.819 0.973-2.521 1.681s-1.261 1.548-1.681 2.521c-0.419 0.973-0.628 1.996-0.628 3.070s0.209 2.097 0.628 3.070c0.419 0.973 0.978 1.813 1.681 2.521s1.543 1.269 2.521 1.681 2.003 0.619 3.070 0.619z'/></symbol><symbol id='ic-tradent' viewbox='0 0 39 28'><title>tradent</title><path d='M4.006 0h11.997c0.569 0.136 1.071 0.33 1.505 0.581s0.789 0.526 1.069 0.825c0.278 0.298 0.499 0.657 0.662 1.078s0.278 0.827 0.346 1.22c0.068 0.393 0.088 0.863 0.061 1.412s-0.075 1.044-0.142 1.484c-0.068 0.441-0.176 0.972-0.325 1.595s-0.285 1.159-0.407 1.606c-0.122 0.447-0.271 0.999-0.447 1.656s-0.319 1.191-0.427 1.597c-0.163 0.624-0.355 1.609-0.578 2.957s-0.465 2.512-0.723 3.488c-0.258 0.976-0.596 1.769-1.017 2.379-0.434 0.664-1.001 1.179-1.699 1.545s-1.423 0.551-2.176 0.56c-0.752 0.007-1.482-0.108-2.187-0.346s-1.328-0.639-1.871-1.209c-0.542-0.569-0.915-1.24-1.118-2.013-0.163-0.8-0.224-1.491-0.183-2.074s0.169-1.238 0.386-1.963c0.217-0.725 0.441-1.376 0.671-1.952s0.45-1.261 0.662-2.054c0.21-0.793 0.328-1.55 0.357-2.266-0.759-0.014-1.437-0.041-2.033-0.081s-1.213-0.115-1.85-0.224c-0.637-0.108-1.193-0.264-1.667-0.468s-0.915-0.454-1.322-0.752c-0.407-0.298-0.739-0.676-0.996-1.13-0.26-0.454-0.443-0.974-0.551-1.557v-1.891c0.61-2.074 1.945-3.409 4.006-4.006v0zM39.367 5.47v0.854c-0.746 3.416-2.643 5.66-5.694 6.731 0.163 0.325 0.4 0.779 0.712 1.362s0.603 1.125 0.874 1.627c0.271 0.502 0.565 1.084 0.883 1.749s0.596 1.281 0.834 1.85c0.237 0.569 0.445 1.179 0.621 1.83s0.28 1.245 0.314 1.78c0.034 0.535-0.009 1.069-0.131 1.597s-0.339 0.992-0.651 1.392c-0.312 0.4-0.759 0.752-1.342 1.057s-1.288 0.54-2.115 0.703h-2.521c-0.976-0.217-1.866-0.554-2.673-1.008s-1.505-1.003-2.094-1.647c-0.59-0.644-1.121-1.333-1.595-2.065s-0.915-1.539-1.322-2.42-0.786-1.719-1.139-2.512c-0.352-0.793-0.752-1.64-1.2-2.542s-0.895-1.683-1.342-2.347c0.42-1.234 0.967-3.274 1.638-6.121s1.249-4.941 1.74-6.283c0.8 0.041 1.636 0.063 2.51 0.070s1.755-0.020 2.643-0.081c0.888-0.061 1.774-0.086 2.652-0.072 0.881 0.014 1.719 0.038 2.512 0.072s1.534 0.145 2.225 0.334c0.691 0.19 1.297 0.445 1.819 0.764s0.947 0.759 1.272 1.322c0.323 0.565 0.513 1.231 0.567 2.004v0z'/></symbol><symbol id='ic-messenger' viewbox='0 0 28 28'><path d='M14 0c-7.732 0-14 5.803-14 12.963 0 4.081 2.035 7.716 5.214 10.096v4.941l4.769-2.616c1.274 0.35 2.62 0.541 4.017 0.541 7.731 0 14-5.804 14-12.962s-6.269-12.964-14-12.964zM15.39 17.457l-3.564-3.803-6.957 3.803 7.652-8.123 3.653 3.802 6.87-3.802-7.654 8.123z'/></symbol><symbol id='ic-blogger' viewbox='0 0 28 28'><path d='M26.19 10.5h-1.57c-0.962 0-1.804-0.813-1.87-1.75v0c0-4.995-4.027-8.75-9.056-8.75h-4.589c-5.026 0-9.102 4.047-9.105 9.042v9.92c0 4.994 4.079 9.038 9.105 9.038h9.801c5.030 0 9.094-4.044 9.094-9.038v-6.409c0-0.998-0.805-2.053-1.81-2.053zM8.75 7h5.25c0.963 0 1.75 0.788 1.75 1.75s-0.787 1.75-1.75 1.75h-5.25c-0.962 0-1.75-0.787-1.75-1.75s0.788-1.75 1.75-1.75zM19.25 21h-10.5c-0.962 0-1.75-0.787-1.75-1.75s0.788-1.75 1.75-1.75h10.5c0.963 0 1.75 0.787 1.75 1.75s-0.787 1.75-1.75 1.75z'/></symbol><symbol id='lighticon' viewbox='0 0 24 24'><path d='m13 10.5c0-.55-.45-1-1-1s-1 .45-1 1v1.25h2z'/><path d='m2.71 15.85v2.68c0 1.52 1.24 2.76 2.76 2.76h2.68l1.9 1.9c.52.52 1.22.81 1.95.81s1.43-.29 1.95-.81l1.9-1.9h2.68c1.52 0 2.76-1.24 2.76-2.76v-2.68l1.9-1.9c.52-.52.81-1.22.81-1.95s-.29-1.43-.81-1.95l-1.9-1.9v-2.68c0-1.52-1.24-2.76-2.76-2.76h-2.68l-1.9-1.9c-1.04-1.04-2.86-1.04-3.9 0l-1.9 1.9h-2.68c-1.52 0-2.76 1.24-2.76 2.76v2.68l-1.9 1.9c-.52.52-.81 1.22-.81 1.95s.29 1.43.81 1.95zm6.79-5.35c0-1.38 1.12-2.5 2.5-2.5s2.5 1.12 2.5 2.5v4.75c0 .41-.34.75-.75.75s-.75-.34-.75-.75v-2h-2v2c0 .41-.34.75-.75.75s-.75-.34-.75-.75z'/></symbol><symbol id='darkicon' viewbox='0 0 24 24'><path d='m2.71 15.85v2.68c0 1.52 1.24 2.76 2.76 2.76h2.68l1.9 1.9c.52.52 1.22.81 1.95.81s1.43-.29 1.95-.81l1.9-1.9h2.68c1.52 0 2.76-1.24 2.76-2.76v-2.68l1.9-1.9c.52-.52.81-1.22.81-1.95s-.29-1.43-.81-1.95l-1.9-1.9v-2.68c0-1.52-1.24-2.76-2.76-2.76h-2.68l-1.9-1.9c-1.04-1.04-2.86-1.04-3.9 0l-1.9 1.9h-2.68c-1.52 0-2.76 1.24-2.76 2.76v2.68l-1.9 1.9c-.52.52-.81 1.22-.81 1.95s.29 1.43.81 1.95zm9.29-9.85c.8 0 1.57.15 2.28.43.29.12.47.4.47.7 0 .31-.19.59-.48.7-1.58.62-3.27 2.04-3.27 4.17s1.69 3.55 3.27 4.17c.29.11.48.39.48.7 0 .3-.18.58-.47.7-.71.28-1.48.43-2.28.43-3.48 0-6-2.52-6-6s2.52-6 6-6z'/></symbol><symbol id="ic-tiktok" viewBox="0 0 32 32"><path d="M30.020 8.024c-1.826 0-3.511-0.605-4.864-1.626-1.552-1.17-2.667-2.886-3.061-4.864-0.098-0.489-0.15-0.993-0.155-1.51h-5.217v14.255l-0.006 7.808c0 2.087-1.359 3.858-3.244 4.48-0.547 0.181-1.137 0.266-1.752 0.233-0.785-0.043-1.521-0.28-2.16-0.663-1.361-0.814-2.283-2.29-2.308-3.979-0.039-2.639 2.094-4.791 4.732-4.791 0.521 0 1.021 0.085 1.489 0.239v-5.297c-0.494-0.073-0.996-0.111-1.504-0.111-2.887 0-5.587 1.2-7.517 3.362-1.459 1.634-2.334 3.718-2.469 5.904-0.177 2.871 0.874 5.601 2.911 7.614 0.299 0.296 0.614 0.57 0.942 0.823 1.747 1.344 3.882 2.073 6.132 2.073 0.508 0 1.011-0.038 1.504-0.111 2.101-0.311 4.040-1.273 5.57-2.786 1.88-1.858 2.919-4.325 2.93-6.951l-0.027-11.66c0.897 0.692 1.878 1.264 2.93 1.709 1.637 0.691 3.372 1.041 5.159 1.040v-5.194c0.001 0.001-0.014 0.001-0.015 0.001z"></path></symbol><symbol id="ic-discord" viewBox="0 0 32 32"><path d="M27.089 5.826c-2.040-0.936-4.227-1.625-6.514-2.020-0.042-0.008-0.083 0.011-0.105 0.050-0.281 0.5-0.593 1.153-0.811 1.666-2.46-0.368-4.907-0.368-7.316 0-0.218-0.524-0.541-1.166-0.824-1.666-0.021-0.037-0.063-0.056-0.105-0.050-2.286 0.394-4.473 1.083-6.513 2.020-0.018 0.008-0.033 0.020-0.043 0.037-4.149 6.198-5.285 12.243-4.728 18.214 0.003 0.029 0.019 0.057 0.042 0.075 2.737 2.010 5.388 3.23 7.991 4.039 0.042 0.013 0.086-0.003 0.112-0.037 0.616-0.841 1.164-1.727 1.635-2.659 0.028-0.055 0.001-0.119-0.056-0.141-0.87-0.33-1.699-0.733-2.496-1.19-0.063-0.037-0.068-0.127-0.010-0.17 0.168-0.126 0.336-0.256 0.496-0.389 0.029-0.024 0.069-0.029 0.103-0.014 5.237 2.391 10.907 2.391 16.082 0 0.034-0.017 0.074-0.011 0.105 0.013 0.16 0.132 0.328 0.264 0.497 0.39 0.058 0.043 0.054 0.133-0.009 0.17-0.797 0.466-1.626 0.86-2.497 1.189-0.057 0.022-0.082 0.088-0.054 0.142 0.481 0.931 1.029 1.817 1.633 2.658 0.025 0.036 0.071 0.051 0.112 0.038 2.615-0.809 5.266-2.029 8.003-4.039 0.024-0.018 0.039-0.044 0.042-0.074 0.667-6.903-1.118-12.899-4.731-18.214-0.009-0.018-0.024-0.030-0.041-0.038zM10.693 20.442c-1.577 0-2.876-1.448-2.876-3.225s1.274-3.225 2.876-3.225c1.614 0 2.901 1.46 2.876 3.225 0 1.778-1.274 3.225-2.876 3.225zM21.326 20.442c-1.577 0-2.876-1.448-2.876-3.225s1.274-3.225 2.876-3.225c1.614 0 2.901 1.46 2.876 3.225 0 1.778-1.261 3.225-2.876 3.225z"></path></symbol><symbol id="ic-patreon" viewBox="0 0 32 32"><path d="M0 0.667h5.625v30.667h-5.625z"></path><path d="M20.512 0.667c-6.356 0-11.525 5.164-11.525 11.511 0 6.333 5.169 11.48 11.525 11.48 6.339 0 11.488-5.153 11.488-11.48 0-6.345-5.151-11.511-11.488-11.511z"></path></symbol></svg>
    <!--TempEnd-->

    <!-- Temp Scripts -->
    <b:tag name='script' type='text/javascript'>var AltImage='<b:include name='altImage'/>',blogTitle='<data:blog.title.jsEscaped/>', BlogLang='<data:blog.locale/>',BlogDirection='<data:blog.languageDirection/>',blogUrl='<data:blog.homepageUrl.canonical/>',CanUrl='<data:blog.canonicalUrl/>',AltAuthor='<b:include name='altAuthor'/>',snippetLength=<b:eval expr='data:blog.isMobileRequest == "true" ? 92 : 110'/>,showAuthor=<b:eval expr='data:widgets.Blog.first.allBylineItems.author ? true : false'/>,showTimestamp=<b:eval expr='data:widgets.Blog.first.allBylineItems.timestamp ? true : false'/>,LinkCopied='<data:messages.linkCopiedToClipboard.escaped/>',showMore='<data:messages.showMore.escaped/>',JumpButton='<data:blog.jumpLinkMessage/>',FeedEnabled=<b:eval expr='data:blog.feedLinks.length gt 3'/>,isPrivate=<data:blog.isPrivateBlog/>,httpsEnabled=<data:blog.httpsEnabled/>,isHomepage=<data:view.isHomepage/>,isArchive=<data:view.isArchive/>,isMultipleItems=<data:view.isMultipleItems/>,isSingleItem=<data:view.isSingleItem/>,isPage=<data:view.isPage/>,isPost=<data:view.isPost/>,blogId='<data:blog.blogId/>',itemId='<b:eval expr='data:view.isSingleItem ? (data:view.isPost ? data:view.postId : data:view.pageId) : ""'/>',theme_color='<data:skin.vars.body_background_color/>',back_color='<data:skin.vars.main_back/>',AnalyticsID='<data:blog.analyticsAccountNumber/>',isStorage=(function(){try{localStorage.check='true';return true}catch(err){return false}})(),SpeedFirst=SqCmz['speed-first']!= undefined?SqCmz['speed-first']:false;/*<![CDATA[*/
      (function(){var p=b;(function(c,d){var o=b,e=c();while(!![]){try{var f=-parseInt(o(0x1e1))/0x1+-parseInt(o(0x161))/0x2+parseInt(o(0x158))/0x3*(-parseInt(o(0x1b1))/0x4)+parseInt(o(0x19b))/0x5*(parseInt(o(0x18f))/0x6)+-parseInt(o(0x131))/0x7*(-parseInt(o(0x1bf))/0x8)+-parseInt(o(0x1cb))/0x9*(-parseInt(o(0x171))/0xa)+-parseInt(o(0x1da))/0xb*(parseInt(o(0x129))/0xc);if(f===d)break;else e['push'](e['shift']());}catch(g){e['push'](e['shift']());}}}(a,0x5199a));SqCmz[p(0x1ce)]==!![]?window['_$']=function(c){var q=b,d=document[q(0x1db)](c);if(0x1<d[q(0x153)])return d;else return d[q(0x153)]==0x0?document[q(0x14a)]()[q(0x180)]:d[0x0];}:window['_$']=$;_$(p(0x11b))[p(0x120)](function(c){var r=b;c[r(0x1a4)]=trans[c[r(0x160)](r(0x175))];}),_$(p(0x148))[p(0x120)](function(c){var s=b;c[s(0x1d8)]=trans[c[s(0x160)](s(0x155))];});if(isPost){if(SqCmz[p(0x169)]!=![]&&document[p(0x1d5)](p(0x1b7))==null){var heads=_$(p(0x19f))[p(0x1db)](p(0x15e));0x0<heads[p(0x153)]&&(_$(p(0x162))[p(0x1ac)]=p(0x1cc)+trans[0x23]+p(0x1d1),heads[p(0x120)](function(c,d){var t=b;c[t(0x1c7)]('id',t(0x197)+(d+0x1)),_$(t(0x14e))[t(0x16a)](t(0x12c)+c[t(0x119)]+t(0x1a0)+(d+0x1)+'\x22>'+c[t(0x1a4)]+t(0x140));}));}_$(p(0x1d9))[p(0x18e)](function(c){var u=b;this[u(0x18d)][u(0x1ad)](u(0x187));}),_$(p(0x15b))[p(0x18e)](function(c){var v=b;c[v(0x1d0)]();var d=document[v(0x1d5)](this[v(0x160)](v(0x139))),f=d[v(0x1e2)](),g=SqCmz[v(0x147)]==!![]?f[v(0x1a7)]-document[v(0x1d5)](v(0x18c))[v(0x1c8)]:f[v(0x1a7)];g=SqCmz[v(0x1e3)]==!![]?f[v(0x1a7)]-document[v(0x1d5)](v(0x1c3))[v(0x1c8)]:f[v(0x1a7)],window[v(0x12d)]({'behavior':v(0x1e7),'left':0x0,'top':g+window[v(0x12a)]});});}isSingleItem&&(p(0x128)in HTMLImageElement[p(0x194)]&&_$(p(0x1e9))[p(0x120)](function(c){var w=b;c[w(0x1c7)](w(0x128),w(0x191));}),_$(p(0x15a))[p(0x18e)](function(c){var x=b;c[x(0x1d0)]();}),_$(p(0x1b4))[p(0x120)](function(c){var y=b,d=document[y(0x13b)](y(0x11e));d[y(0x154)](y(0x1b6)),c[y(0x1bb)](d),d[y(0x16a)](c);}));window[p(0x12e)]=function(c,d){var z=b;return c[z(0x1d0)](),window[z(0x187)](d[z(0x139)],z(0x170),z(0x1e4)),![];};var searchForm=_$(p(0x13e));searchForm[p(0x1a8)]=function(){var A=b;searchForm[A(0x1ba)][A(0x1e8)](A(0x157))?searchForm[A(0x1d5)](A(0x181))[A(0x136)]?searchForm[A(0x15c)]():searchForm[A(0x1ba)][A(0x16b)](A(0x157)):(searchForm[A(0x1ba)][A(0x183)](A(0x157)),searchForm[A(0x1d5)](A(0x181))[A(0x188)]());},_$(p(0x1aa))[p(0x120)](function(c){var B=b;c[B(0x1c7)](B(0x1d8),c[B(0x160)](B(0x1d8))[B(0x18b)](/(<[^>]*>|_)/g,''));}),_$(p(0x135))[p(0x120)](function(c){var C=b;c[C(0x1d5)]('a')[C(0x1ac)]=c[C(0x1d5)]('a')[C(0x1ac)][C(0x18b)](/_/g,'');});var submenus=_$(p(0x167));submenus[p(0x120)](function(c){var D=b;c[D(0x1d5)]('ul')?!c[D(0x1d5)](D(0x1c9))&&(c[D(0x1b9)](c[D(0x1d5)]('ul')),c[D(0x1a1)](D(0x1cf))):c[D(0x1a1)](D(0x1cf));});if(window[p(0x11d)](p(0x178))[p(0x199)]){var dropMenu=document[p(0x1db)](p(0x174));dropMenu[p(0x138)](function(c){var E=b;c[E(0x164)](E(0x1ec),function(d){var F=b;d[F(0x1d0)](),this[F(0x18d)][F(0x1ad)](F(0x17c));});});}_$(p(0x1de))[p(0x120)](function(c){var G=b,d=c[G(0x14f)]('ul')[0x0];c[G(0x19c)]=function(){var H=b;0x3e0<window[H(0x15d)]&&(BlogDirection==H(0x189)?this[H(0x1e2)]()[H(0x137)]<0xc8&&(d[H(0x1c2)][H(0x143)]=H(0x179),d[H(0x1c2)][H(0x137)]=H(0x15f)):window[H(0x15d)]-this[H(0x1e2)]()[H(0x143)]<0xc8&&(d[H(0x1c2)][H(0x137)]=H(0x179),d[H(0x1c2)][H(0x143)]=H(0x15f)));},c[G(0x186)]=function(){var I=b;d[I(0x1a1)](I(0x1c2));};}),_$(p(0x1c6))[p(0x120)](function(c){var J=b,d=c[J(0x1d5)]('a'),e=decodeURIComponent(d[J(0x139)][J(0x13f)]('#')[0x1]);d[J(0x1c7)](J(0x11f),e),d[J(0x139)]=J(0x1df)+e+J(0x156)+(SqCmz[J(0x163)]||0xa);}),_$(p(0x1c1))[p(0x1a8)]=function(){var K=b;_$(K(0x1be))[K(0x1ad)](K(0x187));},_$(p(0x133))[p(0x1a8)]=function(){var L=b;_$(L(0x1ca))[L(0x1ad)](L(0x187));},_$(p(0x1a6))[p(0x120)](function(c){var M=b,d=c[M(0x160)](M(0x1d4));if(d[M(0x1dd)](M(0x16d))!=-0x1){var e=d[M(0x173)](/\[.+?\]/g);e=e[0x1]?e[0x1][M(0x18b)](/(\[|\])/g,''):0x3,c[M(0x18d)][M(0x1d5)](M(0x16e))[M(0x154)](M(0x1ea)+e);}var f=d[M(0x18b)](/(\[SOC\]|\[ACC\]|\[GAL.+\])/,'');c[M(0x1c7)](M(0x1d4),f),c[M(0x1d5)]('h4')[M(0x1a4)]=f;}),_$(p(0x1ed))[p(0x120)](function(c){var N=b,d=[N(0x118),N(0x165),N(0x1d2),N(0x142),N(0x1b3),N(0x11a),N(0x17a),N(0x198),N(0x134),'x'],e=c[N(0x18a)],f=e[N(0x1dd)]('-')!=-0x1?e[N(0x173)](/.+-/)[0x0][N(0x176)](0x0,-0x1):e,g=e[N(0x13f)]('-')[e[N(0x13f)]('-')[N(0x153)]-0x1]||e,h;d[N(0x1dd)](f)!=-0x1?h=N(0x127)+f+N(0x172)+f+N(0x12f):h=N(0x121)+f+N(0x1ab),c[N(0x1ac)]=h+N(0x130)+g+N(0x1d7),c[N(0x149)](N(0x19e))[N(0x16f)](N(0x1a5));});var noCrop=SqCmz[p(0x1cd)];window[p(0x190)]=function(c,d,g){var O=b,i;try{i=SqCmz[O(0x192)]||![];}catch(l){i=![];}try{var j=i?O(0x141):O(0x124),k=d===g?'s'+d+'-c'+j:'w'+parseInt(d)+'-h'+parseInt(g)+(noCrop?O(0x144):'-p')+j;if(c[O(0x1dd)](O(0x16c))!==-0x1||c[O(0x1dd)](O(0x1bd))!==-0x1)c=c[O(0x18b)](O(0x13c),O(0x1e0));else c[O(0x1dd)](O(0x12b))!==-0x1&&c[O(0x1dd)]('=')!=-0x1?c=c[O(0x18b)](c[O(0x13f)]('=')[c[O(0x13f)]('=')[O(0x153)]-0x1],k):c[O(0x18b)](/(:\/\/)/,'')[O(0x13f)]('/')[O(0x153)]<0x7?c=c[O(0x18b)](c[O(0x13f)]('/')[c[O(0x13f)]('/')[O(0x153)]-0x1],k+'/'+c[O(0x13f)]('/')[c[O(0x13f)]('/')[O(0x153)]-0x1]):c=c[O(0x18b)](c[O(0x13f)]('/')[c[O(0x13f)]('/')[O(0x153)]-0x2],k);}finally{return c;}},window[p(0x17e)]=function(c,d,e,f){var P=b;e?g():document[P(0x164)](P(0x12d),g);function g(){var Q=b,h=f?f[Q(0x1db)](Q(0x123)+c+']'):document[Q(0x1db)](Q(0x123)+c+']');for(var i=0x0;i<h[Q(0x153)];i++){var j=h[i],k=j[Q(0x1e2)]()[Q(0x1a7)]-document[Q(0x125)][Q(0x1e2)]()[Q(0x1a7)],l,m;if(k<window[Q(0x12a)]+window[Q(0x1b0)]||e){if(d)l=j[Q(0x149)](d)[Q(0x1b5)],m=j[Q(0x149)](d)[Q(0x1c8)];else{if(j[Q(0x132)](Q(0x152))&&j[Q(0x132)](Q(0x159)))l=Math[Q(0x126)](j[Q(0x160)](Q(0x152))),m=Math[Q(0x126)](j[Q(0x160)](Q(0x159)));else j[Q(0x132)](Q(0x1c5))&&j[Q(0x132)](Q(0x151))?(l=Math[Q(0x126)](j[Q(0x160)](Q(0x1c5))),m=Math[Q(0x126)](j[Q(0x160)](Q(0x151)))):(l=Math[Q(0x126)](j[Q(0x18d)][Q(0x1b5)]),m=Math[Q(0x126)](j[Q(0x18d)][Q(0x1c8)]));}var n=resizeImg(j[Q(0x160)](c),l,m);j[Q(0x1c7)](Q(0x196),n),j[Q(0x1a1)](c),j[Q(0x1a1)](Q(0x1cf)),j[Q(0x1a1)](Q(0x1c2)),j[Q(0x18d)][Q(0x1ba)][Q(0x16b)](Q(0x1e6));}}}},LazyImages(p(0x166)),_$(p(0x1c0))[p(0x18e)](function(){var R=b;this[R(0x18d)][R(0x1ad)](R(0x187));}),_$(p(0x1ee))[p(0x18e)](function(){var S=b;_$(S(0x125))[S(0x1ad)]('dm'),localStorage[S(0x17b)]=document[S(0x125)][S(0x193)]('dm')?!![]:![];}),_$(p(0x150))[p(0x18e)](function(){var T=b;_$(T(0x125))[T(0x1ad)](T(0x1a2)),localStorage[T(0x13d)]=document[T(0x125)][T(0x193)](T(0x1a2))?!![]:![];});if(SqCmz[p(0x1af)]!=![]){var initClicks=!![];TurnOn(),document[p(0x164)](p(0x17f),function(c){var U=b;if(c[U(0x1d3)]==0x7b)c[U(0x1d0)]();else{if(c[U(0x14b)]&&c[U(0x1c4)]&&c[U(0x1d3)]==0x49)c[U(0x1d0)]();else c[U(0x14b)]&&c[U(0x1c4)]&&c[U(0x1d3)]==0x4a&&c[U(0x1d0)]();}}),_$(p(0x1bc))[p(0x120)](function(c){var V=b;c[V(0x164)](V(0x1b2),TurnOff),c[V(0x164)](V(0x14c),TurnOn);});function TurnOn(){var W=b;initClicks&&(initClicks=![],document[W(0x164)](W(0x1dc),function(c){var X=b;c[X(0x184)][X(0x177)]!==X(0x185)&&c[X(0x1d0)]();})),document[W(0x125)][W(0x1c7)](W(0x168),W(0x195)),document[W(0x125)][W(0x1c7)](W(0x1e5),W(0x17d)),document[W(0x125)][W(0x1c7)](W(0x14d),W(0x17d));}function TurnOff(){var Y=b;document[Y(0x125)][Y(0x1c7)](Y(0x168),Y(0x1eb)),document[Y(0x125)][Y(0x1a1)](Y(0x1e5)),document[Y(0x125)][Y(0x1a1)](Y(0x14d));}}var scriptsInited=![];function b(c,d){var e=a();return b=function(f,g){f=f-0x118;var h=e[f];return h;},b(c,d);}isStorage&&sessionStorage[p(0x1a3)]?SpeedFirst?!scriptsInited&&(0x0<window[p(0x13a)]?(scriptsInited=!![],document[p(0x122)]==p(0x128)?document[p(0x164)](p(0x146),function(){var Z=b;eval(sessionStorage[Z(0x1a3)]);}):eval(sessionStorage[p(0x1a3)])):window[p(0x1a9)](function(){var a0=b;scriptsInited=!![],eval(sessionStorage[a0(0x1a3)]);})):document[p(0x164)](p(0x146),function(){var a1=b;eval(sessionStorage[a1(0x1a3)]);}):SpeedFirst?!scriptsInited&&(0x0<window[p(0x13a)]?(scriptsInited=!![],GetScripts()):window[p(0x1a9)](function(){scriptsInited=!![],GetScripts();})):GetScripts();function GetScripts(){var a2=b;fetch(a2(0x19a))[a2(0x19d)](c=>c[a2(0x1ae)]())[a2(0x19d)](c=>{var a3=b;eval(c),sessionStorage[a3(0x1a3)]=c;});}SqCmz[p(0x1d6)]&&_$(p(0x145))[p(0x1b8)](0x190,function(){var a4=b;_$(a4(0x125))[a4(0x1c7)](a4(0x182),a4(0x195)),_$(a4(0x11c))[a4(0x1b8)](0x190,function(c){var a5=b;c[a5(0x16b)]();});});function a(){var a6=['expanded','return\x20false;','LazyImages','keydown','childNodes','input','data-overflow','add','target','CODE','onmouseout','open','focus','rtl','textContent','replace','#LinkList302','parentNode','onClick','84MCwcVI','resizeImg','lazy','support-webp','hasClass','prototype','true','src','head-','tiktok','matches','https://squeezetemplate.github.io/assets/<EMAIL>?sqV=1','218970VJpxWt','onmouseover','then','.social-widget','.post-body','\x22><a\x20href=\x22#head-','removeAttribute','boxed','scripts','innerText','hide','[data-title*=\x22[SOC]\x22],[data-title*=\x22[ACC]\x22],[data-title*=\x22[GAL]\x22]','top','onclick','oneScroll','#menu-bar\x20ul\x20li\x20>\x20a','\x27></i>','innerHTML','toggleClass','text','protect','innerHeight','8BufZxd','mouseenter','messenger','iframe[src*=\x22blogger.com/video\x22],\x20iframe[src*=\x22youtube.com/embed\x22],\x20iframe[src*=\x22vimeo.com/video\x22]','offsetWidth','video-wrapper','.divided-post','fadeOut','removeChild','classList','after','.post-body\x20pre.sq-code','ytimg.com','.menu-res-wrap\x20ul','167848EzxAfD','.b-toggles\x20span','.menu-res\x20button','style','#top-bar','shiftKey','data-original-width','.MegaItem','setAttribute','offsetHeight','ul\x20li','#menu-bar\x20.menu-bar\x20>\x20ul','1107piwloz','<span>','no-crop','jquery','class','preventDefault','</span><nav><ul></ul></nav>','tradent','keyCode','data-title','querySelector','spinner','</div>','title','#TOC>span','1943414KFPEIL','querySelectorAll','contextmenu','indexOf','.bot-menu-st','/search/label/','/mqdefault','626007KLEPio','getBoundingClientRect','topbar-fixed','height=570,width=600','oncopy','PLHolder','smooth','contains','.post-body\x20img','gal-','false','click','.social-widget\x20a','#bt-scheme','khamsat','localName','blogger','[data-trans]','.Loading','matchMedia','div','data-label','each','<i\x20class=\x27fa\x20fa-','readyState','img[','-e90','body','ceil','<i\x20class=\x27fa-','loading','12vhgZXR','pageYOffset','googleusercontent','<li\x20data-tag=\x22','scroll','popUp','\x27/></svg></i>','<div>','119DVnqhk','hasAttribute','.menu-bar-res','patreon','#menu-bar\x20.sitem,\x20#menu-bar\x20.ssitem','value','left','filter','href','scrollY','createElement','/default','isBoxed','.search','split','</a></li>','-e90-rw','google-play','right','-pa','.Loading\x20>\x20div','DOMContentLoaded','menu-fixed','[data-trans-title]','closest','createDocumentFragment','ctrlKey','mouseleave','oncut','#TOC\x20ul','getElementsByTagName','#bt-boxing','data-original-height','width','length','addClass','data-trans-title','?max-results=','open-search','227505Iflawa','height','.separator\x20a','#TOC\x20li\x20a','submit','innerWidth','h2,\x20h3,\x20h4,\x20h5,\x20h6','200px','getAttribute','298692QrYoLk','#TOC','max-results','addEventListener','mostaql','data-src','#menu-bar\x20.drop-menu-st,\x20#menu-bar\x20.bot-menu-st','data-protect','toc','append','remove','img.youtube.com','[GAL]','.gallery-widget','removeClass','_blank','38060ULNkgl','\x27><svg><use\x20href=\x27#ic-','match','[class*=\x22menu-st\x22]\x20>\x20a','data-trans','slice','nodeName','(max-width:\x20992px)','auto','discord','isDark'];a=function(){return a6;};return a();}})();

    /*]]>*/</b:tag>

    <!-- Google Analytics -->
    <script>/*<![CDATA[*/window.addEventListener('scroll', function() {$getScript('https://www.googletagmanager.com/gtag/js?id=' + AnalyticsID, function(){ window.dataLayer = window.dataLayer || [];function gtag(){dataLayer.push(arguments);}gtag('js', new Date());gtag('config', AnalyticsID);});}, {once:true});/*]]>*/</script>

    &lt;noscript id='blogger-components'&gt;&lt;!--</body>--&gt;&lt;/noscript&gt;&lt;/body&gt;
</html>